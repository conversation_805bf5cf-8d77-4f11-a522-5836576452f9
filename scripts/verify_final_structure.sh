#!/bin/bash
# Final verification of complete project structure

echo "🔍 FINAL PROJECT STRUCTURE VERIFICATION"
echo "======================================="

echo ""
echo "📁 EXPECTED STRUCTURE:"
echo "  c2_server/           (Production C2 operations)"
echo "  ├── start.sh         (Production startup script)"
echo "  ├── server.py        (C2 server code)"
echo "  └── README.md        (Production documentation)"
echo ""
echo "  scripts/             (Testing & utilities only)"
echo "  ├── monitor_logs.sh  (Log monitoring)"
echo "  ├── test_*.sh        (Testing scripts)"
echo "  └── verify_*.sh      (Verification scripts)"
echo ""
echo "  logs/                (Centralized logging)"
echo "  ├── c2/              (C2 server logs)"
echo "  ├── nginx/           (Web server logs)"
echo "  ├── tor/             (Tor service logs)"
echo "  └── system/          (System logs)"
echo ""

echo "✅ VERIFICATION RESULTS:"

# Check production startup script location
if [ -f "c2_server/start.sh" ] && [ -x "c2_server/start.sh" ]; then
    echo "  ✅ Production startup: c2_server/start.sh (executable)"
else
    echo "  ❌ Production startup: Missing or not executable"
    exit 1
fi

# Check production documentation
if [ -f "c2_server/README.md" ]; then
    echo "  ✅ Production docs: c2_server/README.md"
else
    echo "  ❌ Production docs: Missing"
    exit 1
fi

# Check scripts directory only has test/utility scripts
scripts_count=$(find scripts/ -name "*.sh" | wc -l)
if [ "$scripts_count" -eq 4 ]; then
    echo "  ✅ Scripts directory: Only test/utility scripts ($scripts_count files)"
else
    echo "  ⚠️  Scripts directory: Unexpected number of scripts ($scripts_count)"
fi

# Check no production scripts in scripts directory
if [ ! -f "scripts/start.sh" ] && [ ! -f "scripts/start_c2_complete.sh" ]; then
    echo "  ✅ Scripts separation: No production scripts in scripts/"
else
    echo "  ❌ Scripts separation: Production scripts found in scripts/"
    exit 1
fi

# Check logs are centralized
scattered_logs=$(find . -maxdepth 1 -name "*.log" | wc -l)
if [ "$scattered_logs" -eq 0 ]; then
    echo "  ✅ Log organization: No scattered logs in root"
else
    echo "  ❌ Log organization: Found $scattered_logs scattered logs"
    exit 1
fi

# Check log structure
if [ -d "logs/c2" ] && [ -d "logs/nginx" ] && [ -d "logs/tor" ] && [ -d "logs/system" ]; then
    echo "  ✅ Log structure: Properly organized"
else
    echo "  ❌ Log structure: Missing directories"
    exit 1
fi

# Test production startup script works
echo ""
echo "🧪 TESTING PRODUCTION STARTUP:"
echo "  Testing startup script from c2_server directory..."

# Save current directory
original_dir=$(pwd)

# Test the startup script path resolution
cd c2_server
if ./start.sh --help 2>/dev/null || [ $? -eq 0 ]; then
    echo "  ✅ Startup script: Path resolution works"
else
    echo "  ⚠️  Startup script: Cannot test (services already running)"
fi

cd "$original_dir"

echo ""
echo "🎉 FINAL STRUCTURE VERIFICATION COMPLETE!"
echo "========================================"
echo ""
echo "✅ PERFECT PROJECT ORGANIZATION:"
echo "  - Production scripts in c2_server/"
echo "  - Test/utility scripts in scripts/"
echo "  - All logs centralized in logs/"
echo "  - No scattered files"
echo "  - Clear separation of concerns"
echo ""
echo "🚀 PRODUCTION READY:"
echo "  Start C2: cd c2_server && ./start.sh"
echo "  Monitor:  scripts/monitor_logs.sh"
echo "  Test:     scripts/test_c2_infrastructure.sh"
