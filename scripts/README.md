# Scripts Directory

## 📋 Test & Utility Scripts

This directory contains **testing and utility scripts only**. 
For production operations, use the scripts in their respective service directories.

### Available Scripts

#### 🧪 Testing Scripts
- `test_c2_infrastructure.sh` - Test complete C2 infrastructure
- `verify_clean_c2.sh` - Verify C2 setup is clean and operational
- `verify_logs_fixed.sh` - Verify log structure is properly organized

#### 📊 Monitoring Scripts
- `monitor_logs.sh` - Monitor and analyze logs
  - `./monitor_logs.sh` - Show log summary
  - `./monitor_logs.sh tail` - Live monitor all logs
  - `./monitor_logs.sh errors` - Check for errors

### Production Scripts Location

**For production operations, use:**
- **Start C2**: `../c2_server/start.sh`
- **Rotate Payloads**: `../nginx/payloads/rotate_payloads.sh`

### Usage Guidelines

- ✅ **Use scripts here for**: Testing, verification, monitoring, debugging
- ❌ **Don't use scripts here for**: Production startup, payload deployment
- 📁 **Production scripts are located**: In their respective service directories
