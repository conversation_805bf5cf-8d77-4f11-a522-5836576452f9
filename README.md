# WormV2 - Educational Malware Research Platform

## ⚠️ EDUCATIONAL USE ONLY ⚠️
This project is for cybersecurity research and education purposes only.

## Project Structure
- `c2_server/` - Command & Control server infrastructure
- `payloads/` - Miner binaries and payload wrappers
- `worm/` - Core worm propagation logic
- `persistence/` - Persistence mechanisms
- `evasion/` - Anti-forensics techniques
- `config/` - Configuration files
- `scripts/` - Deployment and testing scripts

## Implementation Phases
1. ✅ C2 Server Setup
2. ⏳ Miner Payload Preparation
3. ⏳ Worm Core Logic
4. ⏳ Persistence Mechanisms
5. ⏳ Forensic Resistance

## Usage
See individual phase documentation in respective directories.
