Jun 12 15:25:19.000 [notice] Tor ******** opening new log file.
Jun 12 15:25:19.036 [notice] We compiled with OpenSSL 101010ef: OpenSSL 1.1.1n  15 Mar 2022 and we are running with OpenSSL 1010117f: 1.1.1w. These two versions should be binary compatible.
Jun 12 15:25:19.037 [notice] Tor ******** running on Linux with Libevent 2.1.12-stable, OpenSSL 1.1.1w, Zlib 1.3.1, Liblzma 5.8.1, Libzstd 1.5.7 and Glibc 2.41 as libc.
Jun 12 15:25:19.037 [notice] Tor can't help you if you use it wrong! Learn how to be safe at https://support.torproject.org/faq/staying-anonymous/
Jun 12 15:25:19.037 [warn] Tor was compiled with zstd 1.4.8, but is running with zstd 1.5.7. For safety, we'll avoid using advanced zstd functionality.
Jun 12 15:25:19.037 [notice] Read configuration file "/home/<USER>/Documents/augment-projects/wormv2/tor/torrc".
Jun 12 15:25:19.038 [warn] Path for DataDirectory (tor/data) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/data. Is this what you wanted?
Jun 12 15:25:19.038 [warn] Path for HiddenServiceDir (tor/hidden_service) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/hidden_service. Is this what you wanted?
Jun 12 15:25:19.038 [warn] CookieAuthFileGroupReadable is set, but will have no effect: you must specify an explicit CookieAuthFile to have it group-readable.
Jun 12 15:25:19.038 [notice] Wow!  I detected that you have 24 CPUs. I will not autodetect any more than 16, though.  If you want to configure more, set NumCPUs in your torrc
Jun 12 15:25:19.038 [notice] Opening Socks listener on 127.0.0.1:9050
Jun 12 15:25:19.038 [notice] Opened Socks listener connection (ready) on 127.0.0.1:9050
Jun 12 15:25:19.038 [notice] Opening Control listener on 127.0.0.1:9051
Jun 12 15:25:19.038 [notice] Opened Control listener connection (ready) on 127.0.0.1:9051
Jun 12 15:25:19.038 [warn] Fixing permissions on directory tor/data
Jun 12 15:25:19.000 [warn] Unhandled OpenSSL errors found at ../src/lib/tls/tortls.c:190: 
Jun 12 15:25:19.000 [warn] TLS error: could not load the shared library (in DSO support routines:dlfcn_load:---)
Jun 12 15:25:19.000 [warn] TLS error: could not load the shared library (in DSO support routines:DSO_load:---)
Jun 12 15:25:19.000 [warn] TLS error: error loading dso (in configuration file routines:module_load_dso:---)
Jun 12 15:25:19.000 [warn] TLS error: unknown module name (in configuration file routines:module_run:---)
Jun 12 15:25:19.000 [notice] Bootstrapped 0% (starting): Starting
Jun 12 15:25:19.000 [notice] Starting with guard context "default"
Jun 12 15:25:20.000 [notice] Bootstrapped 5% (conn): Connecting to a relay
Jun 12 15:25:21.000 [warn] Problem bootstrapping. Stuck at 5% (conn): Connecting to a relay. (No route to host; NOROUTE; count 1; recommendation warn; host A8874E2C45F445DBA462A914ED8D3AF045734FFB at **************:9999)
Jun 12 15:25:23.000 [notice] Bootstrapped 10% (conn_done): Connected to a relay
Jun 12 15:25:23.000 [notice] Bootstrapped 14% (handshake): Handshaking with a relay
Jun 12 15:25:23.000 [notice] Bootstrapped 15% (handshake_done): Handshake with a relay done
Jun 12 15:25:23.000 [notice] Bootstrapped 20% (onehop_create): Establishing an encrypted directory connection
Jun 12 15:25:23.000 [notice] Bootstrapped 25% (requesting_status): Asking for networkstatus consensus
Jun 12 15:25:23.000 [notice] Bootstrapped 30% (loading_status): Loading networkstatus consensus
Jun 12 15:25:23.000 [notice] I learned some more directory information, but not enough to build a circuit: We have no usable consensus.
Jun 12 15:25:23.000 [notice] Bootstrapped 40% (loading_keys): Loading authority key certs
Jun 12 15:25:24.000 [notice] The current consensus has no exit nodes. Tor can only build internal paths, such as paths to onion services.
Jun 12 15:25:24.000 [notice] Bootstrapped 45% (requesting_descriptors): Asking for relay descriptors
Jun 12 15:25:24.000 [notice] I learned some more directory information, but not enough to build a circuit: We need more microdescriptors: we have 0/8502, and can only build 0% of likely paths. (We have 0% of guards bw, 0% of midpoint bw, and 0% of end bw (no exits in consensus, using mid) = 0% of path bw.)
Jun 12 15:25:24.000 [notice] Bootstrapped 50% (loading_descriptors): Loading relay descriptors
Jun 12 15:25:24.000 [notice] The current consensus contains exit nodes. Tor can build exit and internal paths.
Jun 12 15:25:25.000 [notice] Bootstrapped 56% (loading_descriptors): Loading relay descriptors
Jun 12 15:25:25.000 [notice] Bootstrapped 63% (loading_descriptors): Loading relay descriptors
Jun 12 15:25:25.000 [notice] Bootstrapped 68% (loading_descriptors): Loading relay descriptors
Jun 12 15:25:25.000 [notice] Bootstrapped 75% (enough_dirinfo): Loaded enough directory info to build circuits
Jun 12 15:25:26.000 [notice] Bootstrapped 90% (ap_handshake_done): Handshake finished with a relay to build circuits
Jun 12 15:25:26.000 [notice] Bootstrapped 95% (circuit_create): Establishing a Tor circuit
Jun 12 15:25:26.000 [notice] Bootstrapped 100% (done): Done
Jun 12 15:33:06.000 [notice] Catching signal TERM, exiting cleanly.
Jun 12 15:34:33.000 [notice] Tor ******** opening log file.
Jun 12 15:34:33.690 [notice] We compiled with OpenSSL 101010ef: OpenSSL 1.1.1n  15 Mar 2022 and we are running with OpenSSL 1010117f: 1.1.1w. These two versions should be binary compatible.
Jun 12 15:34:33.691 [notice] Tor ******** running on Linux with Libevent 2.1.12-stable, OpenSSL 1.1.1w, Zlib 1.3.1, Liblzma 5.8.1, Libzstd 1.5.7 and Glibc 2.41 as libc.
Jun 12 15:34:33.691 [notice] Tor can't help you if you use it wrong! Learn how to be safe at https://support.torproject.org/faq/staying-anonymous/
Jun 12 15:34:33.691 [warn] Tor was compiled with zstd 1.4.8, but is running with zstd 1.5.7. For safety, we'll avoid using advanced zstd functionality.
Jun 12 15:34:33.691 [notice] Read configuration file "/home/<USER>/Documents/augment-projects/wormv2/tor/torrc".
Jun 12 15:34:33.691 [warn] Path for DataDirectory (tor/data) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/data. Is this what you wanted?
Jun 12 15:34:33.691 [warn] Path for HiddenServiceDir (tor/hidden_service) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/hidden_service. Is this what you wanted?
Jun 12 15:34:33.691 [warn] CookieAuthFileGroupReadable is set, but will have no effect: you must specify an explicit CookieAuthFile to have it group-readable.
Jun 12 15:34:33.692 [notice] Wow!  I detected that you have 24 CPUs. I will not autodetect any more than 16, though.  If you want to configure more, set NumCPUs in your torrc
Jun 12 15:34:33.692 [notice] Opening Socks listener on 127.0.0.1:9050
Jun 12 15:34:33.692 [notice] Opened Socks listener connection (ready) on 127.0.0.1:9050
Jun 12 15:34:33.692 [notice] Opening Control listener on 127.0.0.1:9051
Jun 12 15:34:33.692 [notice] Opened Control listener connection (ready) on 127.0.0.1:9051
Jun 12 15:34:33.000 [warn] Unhandled OpenSSL errors found at ../src/lib/tls/tortls.c:190: 
Jun 12 15:34:33.000 [warn] TLS error: could not load the shared library (in DSO support routines:dlfcn_load:---)
Jun 12 15:34:33.000 [warn] TLS error: could not load the shared library (in DSO support routines:DSO_load:---)
Jun 12 15:34:33.000 [warn] TLS error: error loading dso (in configuration file routines:module_load_dso:---)
Jun 12 15:34:33.000 [warn] TLS error: unknown module name (in configuration file routines:module_run:---)
Jun 12 15:34:33.000 [notice] Bootstrapped 0% (starting): Starting
Jun 12 15:34:33.000 [notice] Starting with guard context "default"
Jun 12 15:34:33.000 [notice] Bootstrapped 5% (conn): Connecting to a relay
Jun 12 15:34:34.000 [notice] Bootstrapped 10% (conn_done): Connected to a relay
Jun 12 15:34:34.000 [notice] Bootstrapped 14% (handshake): Handshaking with a relay
Jun 12 15:34:34.000 [notice] Bootstrapped 15% (handshake_done): Handshake with a relay done
Jun 12 15:34:34.000 [notice] Bootstrapped 75% (enough_dirinfo): Loaded enough directory info to build circuits
