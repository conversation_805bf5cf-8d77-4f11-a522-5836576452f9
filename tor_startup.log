Jun 12 15:34:33.691 [notice] Tor 0.4.5.16 running on Linux with Libevent 2.1.12-stable, OpenSSL 1.1.1w, Zlib 1.3.1, Liblzma 5.8.1, Libzstd 1.5.7 and Glibc 2.41 as libc.
Jun 12 15:34:33.691 [notice] Tor can't help you if you use it wrong! Learn how to be safe at https://support.torproject.org/faq/staying-anonymous/
Jun 12 15:34:33.691 [warn] Tor was compiled with zstd 1.4.8, but is running with zstd 1.5.7. For safety, we'll avoid using advanced zstd functionality.
Jun 12 15:34:33.691 [notice] Read configuration file "/home/<USER>/Documents/augment-projects/wormv2/tor/torrc".
Jun 12 15:34:33.691 [warn] Path for DataDirectory (tor/data) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/data. Is this what you wanted?
Jun 12 15:34:33.691 [warn] Path for HiddenServiceDir (tor/hidden_service) is relative and will resolve to /home/<USER>/Documents/augment-projects/wormv2/tor/hidden_service. Is this what you wanted?
Jun 12 15:34:33.691 [warn] CookieAuthFileGroupReadable is set, but will have no effect: you must specify an explicit CookieAuthFile to have it group-readable.
Jun 12 15:34:33.692 [notice] Wow!  I detected that you have 24 CPUs. I will not autodetect any more than 16, though.  If you want to configure more, set NumCPUs in your torrc
Jun 12 15:34:33.692 [notice] Opening Socks listener on 127.0.0.1:9050
Jun 12 15:34:33.692 [notice] Opened Socks listener connection (ready) on 127.0.0.1:9050
Jun 12 15:34:33.692 [notice] Opening Control listener on 127.0.0.1:9051
Jun 12 15:34:33.692 [notice] Opened Control listener connection (ready) on 127.0.0.1:9051
